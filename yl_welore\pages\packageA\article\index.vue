<template>
	<view>
		<!-- 优化后的头部标题区域 -->
		<cu-custom :isCustom="true" bgColor="none" :isSearch="false">
			<view slot="content" class="header-title" style="color:#000000">
				帖子详情
			</view>
		</cu-custom>

		<!-- 优化后的帖子标题卡片 -->
		<view class="post-title-card">
			<view class="post-title-content" :style="'color:' + (info.study_title_color || '#333')">
				{{ info.study_title }}
			</view>
			<view class="post-title-decoration">✨</view>
		</view>

		<!-- 优化后的用户信息卡片 -->
		<view class="user-info-card" v-if="info">
			<view class="user-avatar-section">
				<view @tap="article_url" data-key="1" class="enhanced-avatar"
					:style="'background-image:url(' + (info.user_head_sculpture) + ');'">
					<view v-if="info.user_id != 0" class="gender-badge"
						:class="info.gender == 2 ? 'female-badge' : 'male-badge'">
						{{ info.gender == 2 ? '♀' : '♂' }}
					</view>
					<image v-if="info.user_id != 0" class="avatar-frame" :src="info.avatar_frame"></image>
				</view>
			</view>

			<view class="user-details-section">
				<view class="user-name-row">
					<text :class="info.user_id != 0 ? info.special : ''" class="user-nickname">
						{{ info.user_nick_name }}
					</text>
					<view class="user-badges">
						<image v-if="info.user_vip == 1 && info.user_id != 0" class="vip-badge"
							:src="(http_root) + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
						<image v-if="info.user_id != 0" class="level-badge" :src="info.level"></image>
						<image v-if="info.wear_merit && info.user_id != 0" class="merit-badge" :src="info.wear_merit">
						</image>
					</view>
				</view>

				<view class="post-meta-row">
					<text class="post-time">🕒 {{ info.adapter_time }}</text>
					<view class="post-status-badges">
						<view v-if="info.topping_time" class="status-badge top-badge">📌 置顶</view>
						<view v-if="info && info.essence_time != 0" class="status-badge essence-badge">⭐ 推荐</view>
					</view>
				</view>
			</view>

			<view @tap="handleCancel2" class="more-actions-btn">
				<text class="more-icon">⋯</text>
			</view>
		</view>
		<!-- 优化后的帖子内容卡片 -->
		<view class="post-content-card">
			<!-- 话题标签 -->
			<view v-if="info.gambit_id" @tap="gambit_list" :data-id="info.gambit_id" class="topic-tag">
				<text class="topic-icon">🏷️</text>
				<text class="topic-name">{{ info.gambit_name }}</text>
			</view>
			<view v-if="info.study_type == 2 && info.check_look == 1" style="text-align: center;">
				<video :show-center-play-btn="true" play-btn-position="center" :unit-id="$state.ad.info.pre_post_id"
					:autoplay="false" id="myVideo" :src="info.study_video"
					:style="'margin:0 auto;width:' + (vd_width) + ';height: ' + (vd_height) + ';border-radius: 10rpx;'">
					<view v-if="copyright.video_download_arbor == 1" @tap.stop.prevent="getOpc"
						class="cicon-cloud-download"
						style="color: #FFFFFF;position: absolute;right: 30rpx;top: 30rpx;font-size: 40rpx;z-index: 10;">
					</view>
				</video>
			</view>
			<view v-if="info.study_type == 6 && info.check_look == 1" style="text-align: center;">
				<channel-video style="width: 100%;" :feed-token="info.feed_token"></channel-video>
			</view>
			<view class="cu-chat" v-if="info.study_type == 1 && info.check_look == 1">
				<view style="
                        margin: 0 auto;
                        overflow: hidden;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        height: 170rpx;
                        width: 100%;
                        background-color: #f6f7f7;
                        border: 1px solid #f0f0f0;
                        border-radius: 10rpx;
                    ">
					<view :style="'background-image: url(' +
						info.user_head_sculpture +
						');background-size: cover;background-position: center;width: 170rpx;background-color: #000;height: 170rpx;'
						">
						<view class="audioOpen" @tap="play" v-if="!info.is_voice" :data-vo="info.study_voice">
							<text style="color: #ffffff; font-size: 15px" class="cicon-play-arrow"></text>
						</view>
						<view class="audioOpen" @tap="stop" v-if="info.is_voice" :data-vo="info.study_voice">
							<text style="color: #ffffff; font-size: 15px" class="cicon-pause"></text>
						</view>
					</view>
					<view style="width: 75%; padding: 20rpx">
						<view style="display: flex; justify-content: space-between; align-items: center">
							<view style="font-size: 28rpx; color: #555555; font-weight: 600">{{ info.user_nick_name
							}}上传的音乐</view>
							<view class="times">{{ info.starttime }}</view>
						</view>
						<view
							style="display: flex; justify-content: space-between; align-items: center; margin-top: 20rpx">
							<view style="font-size: 24rpx; color: #999">{{ info.user_nick_name }}</view>
							<view>
								<slider style="width: 170rpx" @change="sliderChange" block-size="12px" step="1"
									:value="info.offset" :max="info.max" selected-color="#667eea" />
							</view>
						</view>
					</view>
				</view>
			</view>

			<view v-if="info.check_look == 0 && (info.is_buy == 1 || info.is_buy == 3) && info.study_type != 3"
				class="bg-stripes-red" style="text-align: center;margin:20rpx;padding: 40rpx;" @tap="add_goumai">
				<text
					style="vertical-align: middle;background-color: #e54d42;color: #ffffff;padding: 10rpx;">内容需要兑换</text>
			</view>
			<!-- 正常内容 -->
			<view class="img_bord"
				v-if="(info.img_show_type == 0 && info.is_buy == 0 && info.check_look == 0 && info.study_type != 3)">
				<mp-html @linktap="linktap" :show-img-menu="true" :selectable="true" :copy-link="false"
					:lazy-load="true" :content="info.study_content" />
			</view>
			<view v-if="(info.img_show_type == 1 && info.is_buy == 0 && info.check_look == 0 && info.study_type != 3)">
				<view class="img_bord">
					<rich-text style="white-space: pre-line;" :user-select="true" :nodes="info.chun_text"></rich-text>
				</view>
				<view class="bg-white margin-top">
					<view v-if="info.forward_img.length == 1" class="grid col-1 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in info.forward_img"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 2" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in info.forward_img"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 4" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in info.forward_img"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length > 4" class="grid col-3 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in info.forward_img"
							:key="index"></view>
					</view>
				</view>
			</view>
			<!-- 正常内容 -->
			<!-- 活动贴 不隐藏内容 -->
			<view class="img_bord" v-if="(info.img_show_type == 0 && info.is_buy == 2 && info.check_look == 0)">
				<mp-html @linktap="linktap" :show-img-menu="true" :selectable="true" :copy-link="false"
					:lazy-load="true" :content="info.study_content" />
			</view>
			<view v-if="(info.img_show_type == 1 && info.is_buy == 2 && info.check_look == 0)">
				<view class="img_bord">
					<rich-text style="white-space: pre-line;" :user-select="true" :nodes="info.chun_text"></rich-text>
				</view>
				<view class="bg-white margin-top">
					<view v-if="info.forward_img.length == 1" class="grid col-1 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in info.forward_img"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 2" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in info.forward_img"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 4" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in info.forward_img"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length > 2 && info.forward_img.length != 4"
						class="grid col-3 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in info.forward_img"
							:key="index"></view>
					</view>
				</view>
			</view>
			<!-- 活动贴 不隐藏内容 -->
			<!-- 活动贴 不隐藏内容 -->
			<view class="img_bord" v-if="info.img_show_type == 0 && info.check_look == 0 && info.study_type == 3">
				<mp-html @linktap="linktap" :show-img-menu="true" :selectable="true" :copy-link="false"
					:lazy-load="true" :content="info.study_content" />
			</view>
			<view v-if="info.img_show_type == 1 && info.check_look == 0 && info.study_type == 3">
				<view class="img_bord">
					<rich-text style="white-space: pre-line;" :user-select="true" :nodes="info.chun_text"></rich-text>
				</view>
				<view class="bg-white margin-top">
					<view v-if="info.forward_img.length == 1" class="grid col-1 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 2" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 4" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length > 2 && info.forward_img.length != 4"
						class="grid col-3 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
				</view>
			</view>
			<view class="img_bord" v-if="info.img_show_type == 0 && info.check_look == 1 && info.study_type == 3">
				<mp-html @linktap="linktap" :show-img-menu="true" :selectable="true" :copy-link="false"
					:lazy-load="true" :content="info.study_content" />
			</view>
			<view v-if="info.img_show_type == 1 && info.check_look == 1 && info.study_type == 3">
				<view class="img_bord">
					<rich-text style="white-space: pre-line;" :user-select="true" :nodes="info.chun_text"></rich-text>
				</view>
				<view class="bg-white margin-top">
					<view v-if="info.forward_img.length == 1" class="grid col-1 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 2" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 4" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length > 2 && info.forward_img.length != 4"
						class="grid col-3 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
				</view>
			</view>
			<!-- 活动贴 不隐藏内容 -->
			<!-- 隐藏内容 -->
			<view class="img_bord" v-if="info.img_show_type == 0 && info.check_look == 1 && info.study_type != 3">
				<mp-html @linktap="linktap" :show-img-menu="true" :selectable="true" :copy-link="false"
					:lazy-load="true" :content="info.study_content" />
			</view>
			<view v-if="info.img_show_type == 1 && info.check_look == 1 && info.study_type != 3">
				<view class="img_bord">
					<rich-text style="white-space: pre-line;" :user-select="true" :nodes="info.chun_text"></rich-text>
				</view>
				<view class="bg-white margin-top">
					<view v-if="info.forward_img.length == 1" class="grid col-1 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 2" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length == 4" class="grid col-2 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
					<view v-if="info.forward_img.length > 2 && info.forward_img.length != 4"
						class="grid col-3 grid-square">
						<view class="bg-img" @tap="ViewImage" :data-url="item"
							:style="'background-image:url(' + (item) + ');'" v-for="(item, index) in (info.forward_img)"
							:key="index"></view>
					</view>
				</view>
			</view>
			<!-- 隐藏内容 -->
			<view v-if="info.study_type != 2 && img != ''" style="text-align:center;"
				v-for="(img, dataListindex) in (info.image_part)" :key="dataListindex">
				<image :src="img" :data-src="img" @tap="previewImage" mode="widthFix" style="width:100%"></image>
			</view>
		</view>

		<!-- 优化后的位置信息卡片 -->
		<view class="location-card" @tap="get_position" :data-pos_name="info.address_name"
			:data-latitude="info.address_latitude" :data-longitude="info.address_longitude"
			v-if="info.address_name != null">
			<view class="location-content">
				<text class="location-icon">📍</text>
				<text class="location-text">{{ info.address_name }}</text>
				<text class="location-arrow">→</text>
			</view>
		</view>
		<view style="clear:both;height:0"></view>
		<!-- 优化后的投票组件 -->
		<view v-if="info.study_type == 4 || info.study_type == 5" class="modern-vote-container">
			<view class="modern-vote-card">
				<!-- 投票标题区域 -->
				<view class="vote-header-section">
					<view v-if="info.study_type == 4" class="vote-type-badge single-choice">
						<text class="vote-type-icon">🔘</text>
						<text class="vote-type-text">单选投票</text>
					</view>
					<view v-if="info.study_type == 5" class="vote-type-badge multiple-choice">
						<text class="vote-type-icon">☑️</text>
						<text class="vote-type-text">多选投票</text>
					</view>
					<view v-if="info.study_title != ''" class="vote-title">
						{{ info.study_title }}
					</view>
				</view>

				<!-- 投票选项区域 -->
				<view class="vote-options-section">
					<view v-for="(vo_item, vo_index) in info.vo" :key="vo_index" class="vote-option-item"
						@tap.stop.prevent="dian_option" :data-id="vo_item.id" :data-key="dataListindex"
						:data-index="vo_index">
						<!-- 选项内容 -->
						<view class="option-content">
							<view class="option-selector">
								<view class="selector-icon" :class="{
									'selected': isSelected(vo_item.id),
									'single': info.study_type == 4,
									'multiple': info.study_type == 5
								}">
									<text v-if="isSelected(vo_item.id)" class="check-icon">
										{{ info.study_type == 4 ? '●' : '✓' }}
									</text>
								</view>
							</view>

							<view class="option-text">
								{{ vo_item.ballot_name }}
							</view>

							<view v-if="info.is_vo_check > 0" class="option-votes">
								{{ vo_item.voters }}票
							</view>
						</view>

						<!-- 进度条（仅在已投票时显示） -->
						<view v-if="info.is_vo_check > 0" class="option-progress-bar">
							<view class="progress-fill"
								:style="'width:' + ((vo_item.ratio || 0) > 0 ? (vo_item.ratio || 0) : 2) + '%;'">
							</view>
						</view>
					</view>
				</view>

				<!-- 投票信息区域 -->
				<view class="vote-info-section">
					<view class="vote-meta-info">
						<view class="meta-item">
							<view class="meta-icon">👥</view>
							<view class="meta-text">{{ info.vo_count }}人参与</view>
						</view>

						<view v-if="info.vote_deadline != '' && info.vote_deadline != 0 && info.vote_deadline != -1"
							class="meta-item">
							<view class="meta-icon">⏰</view>
							<view class="meta-text">{{ info.vote_deadline }}</view>
						</view>

						<view v-if="info.vote_deadline == -1" class="meta-item expired">
							<view class="meta-icon">🔒</view>
							<view class="meta-text">投票已截止</view>
						</view>
					</view>

					<!-- 投票按钮 -->
					<view v-if="info.is_vo_check == 0" class="vote-action-section">
						<button @tap.stop.prevent="vote_do" :data-index="vo_index" :data-key="0" class="modern-vote-btn"
							:class="{ 'btn-disabled': info.vo_id.length == 0 }">
							<text class="vote-btn-icon">🗳️</text>
							<text class="vote-btn-text">提交投票</text>
						</button>
					</view>

					<view v-if="info.is_vo_check > 0" class="vote-completed-section">
						<view class="completed-badge">
							<text class="completed-icon">✅</text>
							<text class="completed-text">已投票</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 美观简洁的活动详情卡片 -->
		<view class="elegant-activity-container" v-if="info.study_type == 3">
			<view class="elegant-activity-card">
				<!-- 头部认证状态 -->
				<view class="activity-header-section">
					<view class="certification-badge"
						:class="info.brisk_team.is_approve == 1 ? 'certified' : 'uncertified'">
						<text class="cert-icon">{{ info.brisk_team.is_approve == 1 ? '🏆' : '⏳' }}</text>
						<text class="cert-text">{{ info.brisk_team.is_approve == 1 ? '官方认证' : '待认证' }}</text>
					</view>
				</view>

				<!-- 活动信息区域 -->
				<view class="activity-info-grid">
					<!-- 地址信息 -->
					<view class="info-item location-item" @tap="get_position"
						:data-pos_name="info.brisk_team.brisk_address"
						:data-latitude="info.brisk_team.brisk_address_latitude"
						:data-longitude="info.brisk_team.brisk_address_longitude">
						<view class="info-icon">
							<text class="icon-text">📍</text>
						</view>
						<view class="info-details">
							<text class="info-label">活动地址</text>
							<text class="info-value">{{ info.brisk_team.brisk_address }}</text>
						</view>
						<text class="info-arrow cuIcon-right"></text>
					</view>

					<!-- 时间信息 -->
					<view class="info-item">
						<view class="info-icon">
							<text class="icon-text">🕒</text>
						</view>
						<view class="info-details">
							<text class="info-label">活动时间</text>
							<text class="info-value">{{ info.brisk_team.start_time }} - {{ info.brisk_team.end_time
							}}</text>
						</view>
					</view>

					<!-- 人数信息 -->
					<view class="info-row">
						<view class="info-item-small">
							<view class="info-icon-small">
								<text class="icon-text-small">👥</text>
							</view>
							<view class="info-details-small">
								<text class="info-label-small">限制人数</text>
								<text class="info-value-small">{{ info.brisk_team.number_of_people == 0 ? '不限' :
									info.brisk_team.number_of_people }}</text>
							</view>
						</view>
						<view class="info-item-small">
							<view class="info-icon-small">
								<text class="icon-text-small">✋</text>
							</view>
							<view class="info-details-small">
								<text class="info-label-small">已报名</text>
								<text class="info-value-small">{{ info.brisk_count }}</text>
							</view>
						</view>
					</view>

					<!-- 费用信息 -->
					<view class="info-item">
						<view class="info-icon">
							<text class="icon-text">💰</text>
						</view>
						<view class="info-details">
							<text class="info-label">活动费用</text>
							<text class="info-value" :class="info.buy_price == 0 ? 'free-text' : ''">
								{{ info.buy_price == 0 ? '免费参加' : info.buy_price + copyright.currency }}
							</text>
						</view>
					</view>
				</view>

				<!-- 验证码区域 -->
				<!-- <view class="verification-section">
					<view class="verification-card">
						<text class="verification-label">活动验证码</text>
						<text class="verification-code">{{ item.rand_captcha }}</text>
					</view>
				</view> -->

				<!-- 参与者预览 -->
				<view v-if="info.brisk_team.user_head" class="participants-section">
					<view class="participants-header">
						<view class="participants-avatars">
							<view class="participant-avatar"
								:style="'background-image:url(' + (head.user_head_sculpture) + ');'"
								v-for="(head, index) in (info.brisk_team.user_head.slice(0, 6))" :key="index">
							</view>
							<view v-if="info.brisk_team.user_head.length > 6" class="more-participants">
								<text class="more-text">+{{ info.brisk_team.user_head.length - 6 }}</text>
							</view>
						</view>
						<view @tap="set_user_time" class="view-all-btn">
							<text class="view-all-text">报名详情</text>
							<text class="cuIcon-right view-all-arrow"></text>
						</view>
					</view>
				</view>

				<!-- 操作按钮 -->
				<view class="action-section">
					<button v-if="info.brisk_team.overdue == 1" @tap="participation_activities"
						class="elegant-btn join-btn">
						<text class="btn-icon">🎯</text>
						<text class="btn-text">立即参加</text>
					</button>
					<button v-if="info.brisk_team.overdue == 2" class="elegant-btn ended-btn">
						<text class="btn-icon">⏰</text>
						<text class="btn-text">活动已结束</text>
					</button>
					<button v-if="info.brisk_team.overdue == 3" class="elegant-btn joined-btn">
						<text class="btn-icon">✅</text>
						<text class="btn-text">已参加</text>
					</button>
				</view>

				<!-- 参与者详细列表 -->
				<scroll-view @scrolltolower="scrolltolower" v-if="user_time_check" scroll-y
					class="participants-detail-list">
					<view class="participant-detail-item" v-for="(t, index) in user_time" :key="index">
						<view class="participant-info">
							<view class="participant-detail-avatar"
								:style="'background-image:url(' + (t.user_head_sculpture) + ');'"></view>
							<text class="participant-detail-name">{{ t.user_nick_name }}</text>
						</view>
						<text class="participant-detail-time">{{ t.partake_time }}</text>
					</view>
					<view :class="'cu-load ' + (!user_time_msg ? 'loading' : 'over')"></view>
				</scroll-view>
			</view>
		</view>
		<view class="bg-white padding" v-if="info.file_info && open_user_wangpan && version == 0">
			<view class="flex justify-between align-center shadow-warp bg-white" style="border-radius: 5px;">
				<view class="margin-sm">
					<view class="flex align-center">
						<view class="margin-xs">
							<image v-if="info.file_info.is_del == 0"
								:src="(http_root) + 'addons/yl_welore/web/static/file_icon/' + (info.file_info.file_icon)"
								style="height: 35px;width: 35px;"></image>
							<image v-if="info.file_info.is_del == 1"
								:src="(http_root) + 'addons/yl_welore/web/static/file_icon/del.png'"
								style="height: 35px;width: 35px;">
							</image>
						</view>
						<view class="margin-xs" style="margin-left: 20rpx;">
							<block v-if="info.file_info.is_del == 0">
								<view class="font-yl-2 text_num_1" style="font-size: 14px;font-weight: 600;">
									<text>{{ info.file_info.file_name }}</text>
									<text v-if="info.file_info.is_dir == 0">.{{ info.file_info.file_suffix }}</text>
								</view>
								<view style="font-size: 12px;margin-top: 8px;color: #9E9E9E;">
									<text>{{ info.file_info.add_time }}</text>
									<text v-if="info.file_info.is_dir == 0" style="margin-left: 20px;">{{
										info.file_info.file_size
									}}</text>
									<text v-if="info.file_info.is_dir == 1" style="margin-left: 20px;">共{{
										info.file_info.file_count
									}}个文件</text>
								</view>
							</block>
							<block v-if="info.file_info.is_del == 1">
								<view class="font-yl-2 text_num_1" style="font-size: 14px;font-weight: 600;">
									来晚了，文件已被删除！
								</view>
							</block>
						</view>
					</view>
				</view>
				<view class="margin-sm" @tap="open_file" v-if="info.file_info.is_del == 0">
					<text class="cicon-cloud-download text-black" style="font-size: 55rpx;"></text>
				</view>
			</view>
		</view>
		<view style="clear:both;height:0"></view>
		<view class="bg-white" style="padding-bottom:10px;padding-top:50px;text-align:center;"
			v-if="info.red && version == 0">
			<view class="weui-flex"
				style="position: relative;width: 100%;height: 30px;background-color: #E05F4B;border-top-left-radius: 5px;border-top-right-radius: 5px;">
				<view
					style="color: #C65245;position: absolute;width: 95%;margin: auto;background-color: #FBF7EB;text-align: center;left: 0;right: 0;bottom:13px;border-top-left-radius: 5px;border-top-right-radius: 5px;height: 55px;">
					<view style="margin-top:15px;">
						<text class="cicon-redpacket text-red" style="font-size: 20px;vertical-align: middle;"></text>
						<text v-if="info.red.initial_type == 1"
							style="font-size:14px;margin-left:5px;vertical-align: middle;">已领取
							{{ info.red.surplus_quantity }} / {{ info.red.initial_quantity }}，共 {{
								info.red.surplus_fraction }} /
							{{ info.red.initial_fraction }}</text>
						<text v-if="info.red.initial_type == 0"
							style="font-size:14px;margin-left:5px;vertical-align: middle;">已领取
							{{ info.red.surplus_quantity }} / {{ info.red.initial_quantity }}，共 {{
								info.red.surplus_conch }} /
							{{ info.red.initial_conch }}</text>
						<text style="vertical-align: middle;margin-left: 10rpx;">{{ info.red.initial_type == 1 ?
							copyright.confer :
							copyright.currency }}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="padding-xl bg-white" v-if="info.call_phone && version == 0 && open_user_phone">
			<button @tap="open_phone" class="cu-btn block bg-blue lg">
				<text class="cuIcon-dianhua"></text>
				<text style="margin-left: 10rpx">拨打电话</text>
			</button>
		</view>
		<!-- 优化后的点评按钮卡片 -->
		<view v-if="open_user_dianping && info.is_review == 0 && info.user_id != uid" class="review-button-container">
			<view @tap="open_comment" class="review-button-card">
				<view class="review-button-content">
					<view class="review-icon-wrapper">
						<text class="cicon-popover review-icon"></text>
					</view>
					<view class="review-text-section">
						<text class="review-main-text">点评</text>
						<text class="review-sub-text">分享你的看法</text>
					</view>
					<view class="review-arrow">
						<text class="cuIcon-right"></text>
					</view>
				</view>
			</view>
		</view>
		<!-- 优化后的点评列表 -->
		<view class="modern-reviews-container" v-if="open_user_dianping && info">
			<!-- 点评列表标题 -->
			<view class="reviews-header">
				<view class="reviews-title">
					<text class="reviews-icon">💬</text>
					<text class="reviews-text">用户点评</text>
				</view>
				<view class="reviews-count">{{ info.review_score.length != null ? info.review_score.length : 0 }}条评价
				</view>
			</view>

			<!-- 点评卡片列表 -->
			<view class="reviews-list">
				<view class="review-card" v-for="(item, rr_index) in info.review_score" :key="rr_index"
					v-if="info.review_score != ''" @tap="open_comment_info" :data-index="rr_index">
					<!-- 用户信息区域 -->
					<view class="review-user-section">
						<view class="review-avatar-wrapper">
							<image class="review-avatar" :src="item.user_head_sculpture" mode="aspectFill"></image>
							<view class="review-privacy-badge" :class="item.is_show == 0 ? 'private' : 'public'">
								<text class="privacy-icon">{{ item.is_show == 0 ? '🔒' : '👁️' }}</text>
							</view>
						</view>

						<view class="review-user-info">
							<view class="review-username">{{ item.user_nick_name }}</view>
							<view class="review-rating">
								<text class="review-star" :class="item.assess_score > sss_index ? 'active' : ''"
									v-for="(k_item, sss_index) in (5)" :key="sss_index">⭐</text>
							</view>
						</view>

						<view class="review-arrow">
							<text class="cuIcon-right" style="color:#667eea;"></text>
						</view>
					</view>

					<!-- 点评内容区域 -->
					<view class="review-content-section">
						<!-- <view class="review-privacy-tag" :class="item.is_show == 0 ? 'private' : 'public'">
							{{ item.is_show == 0 ? '私密点评' : '公开点评' }}
						</view> -->
						<view class="review-content-text">{{ item.assess_content }}</view>
					</view>
				</view>
			</view>

			<!-- 查看更多按钮 -->
			<view class="more-reviews-section" v-if="info.review_score.length >= 3">
				<view class="more-reviews-btn" @tap="open_more">
					<view class="more-btn-content">
						<text class="more-btn-icon">📋</text>
						<text class="more-btn-text">查看更多点评</text>
						<text class="more-btn-arrow">→</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 优化后的论坛声明区域 -->
		<view v-if="info.is_show_forum_declaration == 1" class="forum-declaration-container">
			<view class="forum-declaration-card">
				<!-- 声明标题区域 -->
				<view class="declaration-header">
					<view class="header-content">
						<text class="header-icon">📋</text>
						<text class="header-title-l">论坛声明</text>
					</view>
					<view class="header-badge">
						<text class="badge-text">重要</text>
					</view>
				</view>

				<!-- 声明内容区域 -->
				<view class="declaration-content">
					<rich-text class="declaration-text" :user-select="true" :nodes="info.forum_declaration">
					</rich-text>
				</view>
			</view>
		</view>
		<!-- <view style="height:1px;width:100%;background-color:#F4F4F4;"></view> -->
		<!-- 优化后的打赏排行榜卡片 -->
		<view v-if="version == 0 && copyright.guard_arbor == 1" class="reward-ranking-card">
			<view class="ranking-header">
				<view class="ranking-title">
					<text class="ranking-icon">🏆</text>
					<text class="ranking-text">打赏排行榜</text>
				</view>
				<view class="ranking-subtitle">感谢以下用户的支持</view>
			</view>

			<view style="display: flex;justify-content: space-between;align-items: center;">
				<view class="ranking-avatars">
					<view v-for="(item, index) in 3" :key="index" @tap="url_is_one" :data-id="info.user_id"
						class="ranking-avatar-item" :class="'rank-' + (index + 1)">
						<view class="avatar-container">
							<image v-if="liwu_ph[index]" :src="liwu_ph[index].user_head_sculpture"
								class="ranking-avatar">
							</image>
							<image v-else src="/static/yl_welore/style/icon/xu.png" class="ranking-avatar placeholder">
							</image>
							<image :src="'/static/yl_welore/style/icon/ic_renwu_no_' + (index + 1) + '.png'"
								class="ranking-badge" mode="widthFix"></image>
						</view>
						<!-- <view class="ranking-position">{{ ['冠军', '亚军', '季军'][index] }}</view> -->
					</view>
				</view>

				<view v-if="copyright.tribute_arbor == 1" class="reward-action">
					<button class="reward-btn" @tap="add_liwu">
						<text class="reward-btn-icon">💝</text>
						<text class="reward-btn-text">打赏支持</text>
					</button>
				</view>
			</view>
		</view>
		<view style="height:1px;width:100%;background-color:#F4F4F4"></view>
		<!-- 优化后的圈子信息区域 -->
		<view v-if="info" class="circle-info-container"
			@click="openUrl('/yl_welore/pages/packageA/circle_info/index?id=' + info.realm_id)">
			<view class="circle-info-card">
				<!-- 圈子头像区域 -->
				<view class="circle-avatar-section">
					<view class="avatar-wrapper">
						<image class="circle-avatar" :src="info.realm_icon" mode="aspectFill"></image>
						<view class="avatar-border"></view>
					</view>
				</view>

				<!-- 圈子信息区域 -->
				<view class="circle-details-section">
					<view class="circle-name">{{ info.realm_name }}</view>
					<view class="circle-stats">
						<view class="stat-item">
							<text class="stat-icon">👥</text>
							<text class="stat-number">{{ info.concern }}</text>
							<text class="stat-label">关注</text>
						</view>
						<view class="stat-divider">|</view>
						<view class="stat-item">
							<text class="stat-icon">📝</text>
							<text class="stat-number">{{ info.paper_number }}</text>
							<text class="stat-label">帖子</text>
						</view>
					</view>
				</view>

				<!-- 进入按钮区域 -->
				<view class="circle-action-section">
					<view class="enter-btn">
						<text class="cuIcon-right" style="color:#ffffff"></text>
					</view>
				</view>
			</view>
		</view>
		<view v-if="$state.ad.info.adsper == 1" style="margin-top:10px;">
			<ad :unit-id="$state.ad.info.adunit_id"></ad>
		</view>
		<!-- 优化后的回复导航栏 -->
		<view class="reply-nav-card">
			<!-- <view class="reply-nav-title">💬 回复列表</view> -->
			<view class="reply-nav-tabs">
				<view data-key="h1" :class="'nav-tab ' + (current_h == 'h1' ? 'active' : '')" @tap="handleChange_h">
					<text class="nav-tab-text">全部回复</text>
				</view>
				<view data-key="h2" :class="'nav-tab ' + (current_h == 'h2' ? 'active' : '')" @tap="handleChange_h">
					<text class="nav-tab-text">只看楼主</text>
				</view>
				<view data-key="h3" :class="'nav-tab ' + (current_h == 'h3' ? 'active' : '')" @tap="handleChange_h">
					<text class="nav-tab-text">我的回复</text>
				</view>
			</view>
		</view>
		<!-- 神回复 -->
		<view style="padding-bottom:3.5em;">
			<!-- 回复列表空状态 -->
			<view v-if="huifu_list.length === 0" class="reply-empty-state">
				<view class="empty-animation-container">
					<view class="empty-icon-main">💬</view>
					<view class="empty-icon-floating">✨</view>
					<view class="empty-icon-floating-2">💭</view>
				</view>
				<view class="empty-content">
					<view class="empty-title">还没有回复哦</view>
					<view class="empty-subtitle">成为第一个回复的人，分享你的想法吧！</view>
				</view>
				<view class="empty-action">
					<view @tap="addhuifu" class="empty-comment-btn">
						<text class="empty-btn-icon">✍️</text>
						<text class="empty-btn-text">写回复</text>
					</view>
				</view>
			</view>

			<!-- 回复列表 -->
			<block v-for="(hui, hui_list_index) in huifu_list" :key="hui_list_index">
				<!-- 优化后的统一回复卡片 -->
				<view class="reply-item-card" :class="hui.reply_type == 1 ? 'voice-reply-card' : ''">
					<!-- 回复头部信息 -->
					<view class="reply-header">
						<view class="reply-avatar-section">
							<view @tap="article_url" data-key="2" :data-id="hui.user_id" class="reply-avatar"
								:style="'background-image:url(' + (hui.user_head_sculpture) + ');'">
								<view v-if="hui.user_id != 0" class="reply-gender-badge"
									:class="hui.gender == 2 ? 'female' : 'male'">
									{{ hui.gender == 2 ? '♀' : '♂' }}
								</view>
								<image v-if="hui.user_id != 0" class="reply-avatar-frame" :src="hui.avatar_frame">
								</image>
							</view>
						</view>
						<view class="reply-user-info">
							<view class="reply-user-name-row">
								<text :class="hui.user_id != 0 ? hui.special : ''" class="reply-username">
									{{ hui.user_nick_name }}
								</text>
								<view class="reply-user-badges">
									<image v-if="hui.user_vip == 1 && hui.user_id != 0" class="reply-vip-badge"
										:src="(http_root) + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
									<image v-if="hui.user_id != 0" class="reply-level-badge" :src="hui.level_info">
									</image>
									<image v-if="hui.wear_merit && hui.user_id != 0" class="reply-merit-badge"
										:src="hui.wear_merit"></image>
								</view>
							</view>

							<view class="reply-meta-row">
								<view v-if="hui.user_id != 0 && hui.is_paper_user == hui.user_id" class="author-badge">
									👑 楼主
								</view>
								<text class="reply-floor">🏢 {{ hui.phase }}楼</text>
								<text class="reply-time">🕒 {{ hui.apter_time	 }}</text>
							</view>
						</view>
						<view class="reply-actions">
							<view class="like-section">
								<text v-if="hui.is_huifu_zan == false" :data-index="hui_list_index" :data-kkk="hui.id"
									class="like-btn" @tap="get_huifu_zan">👍</text>
								<text v-if="hui.is_huifu_zan == true" :data-index="hui_list_index" :data-kkk="hui.id"
									class="like-btn liked" @tap="get_huifu_zan">❤️</text>
								<text class="like-count">{{ hui.is_huifu_zan_count }}</text>
							</view>
							<view>
								<view :data-user_id="hui.user_id" data-type="1" :data-id="hui.id"
									:data-key="hui_list_index" @tap="handleCancel3" class="more-btn">⋯</view>
							</view>
						</view>
					</view>
					<!-- 普通回复内容区域 -->
					<view class="reply-content-section">
						<view class="reply-text-content">
							<rich-text v-if="hui.is_gift == 0" :user-select="true" :nodes="hui.reply_content"
								class="reply-text"></rich-text>
							<view v-if="hui.is_gift == 1" class="reply-gift-content">
								<text class="gift-icon">🎁</text>
								<rich-text class="text-color-my reply-text" :user-select="true"
									:nodes="hui.reply_content"></rich-text>
							</view>
						</view>
						<!-- 回复图片 -->
						<view v-if="hui.image_part[0]" class="reply-image-section">
							<image :data-index="hui_list_index" :src="hui.image_part[0]" :data-src="hui.image_part[0]"
								@tap="previewHuiImage" mode="widthFix" class="reply-image"></image>
						</view>
						<view v-if="hui.reply_type == 1">
							<view class="voice-player-card"
								@tap="parseEventDynamicCode($event, hui.is_voice == false ? 'ting_play' : 'ting_stop')"
								:data-vo="hui.reply_voice" :data-key="hui_list_index">
								<view class="voice-icon-container">
									<text v-if="hui.is_voice == false" class="voice-play-icon">🎵</text>
									<image v-if="hui.is_voice == true" class="voice-playing-gif"
										src="/static/yl_welore/style/icon/home_yuyim1.gif"></image>
								</view>
								<view class="voice-info">
									<text class="voice-label">🎤 语音消息</text>
									<text class="voice-duration">{{ (hui.reply_voice_time == 0 ? 1 :
										hui.reply_voice_time) +
										'"' }}</text>
								</view>
								<view class="voice-wave-animation" v-if="hui.is_voice == true">
									<view class="wave-bar"></view>
									<view class="wave-bar"></view>
									<view class="wave-bar"></view>
									<view class="wave-bar"></view>
								</view>
							</view>
							<!-- 子回复区域 -->
						</view>
						<!-- 子回复区域 -->
						<view v-if="hui.huifu_huifu.length > 0" class="sub-replies-section">
							<view class="sub-replies-header">
								<text class="sub-replies-icon">💬</text>
								<text class="sub-replies-title">回复</text>
							</view>
							<view class="sub-reply-item" v-for="(hhh, index) in (hui.huifu_huifu)" :key="index">
								<text class="sub-reply-username">{{ hhh.user_nick_name }}：</text>
								<rich-text :user-select="true" class="sub-reply-content"
									:nodes="hhh.duplex_content"></rich-text>
							</view>
						</view>
						<!-- 红包标识 -->
						<view v-if="hui.is_red_hui != 0 && version == 0" class="reply-red-badge">
							<text class="red-badge-icon">🧧</text>
							<text class="red-badge-text">{{ hui.is_red_hui }}</text>
						</view>
					</view>

				</view>
				<view style="clear:both;height:0"></view>
			</block>
		</view>
		<view v-if="scene != '1154'" :class="'bg-white cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
		<view v-if="scene == '1154'" style="margin: 30px;" class="cu-load bg-blue">
			<text class="cuIcon-emoji lg"></text>
			更多内容请进入小程序体验
		</view>
		<!-- 神回复 -->
		<!-- 优化后的底部操作栏 -->
		<view v-if="select && scene != '1154'" class="modern-bottom-bar"
			:class="$state.isIphoneX ? 'iphonex-padding' : ''">
			<!-- 评论输入区域 -->
			<view class="comment-input-section">
				<view @tap="addhuifu" class="modern-comment-input">
					<text class="comment-input-placeholder">写评论...</text>
				</view>
			</view>

			<!-- 操作按钮区域 -->
			<view class="action-buttons-section">
				<!-- 回复按钮 -->
				<view @tap="handleReplyButtonClick" class="action-btn-wrapper">
					<view class="action-btn">
						<text class="action-icon">💬</text>
						<view v-if="info.study_repount > 0" class="action-badge yellow">
							{{ info.study_repount >= 99 ? '99+' : info.study_repount }}
						</view>
					</view>
				</view>

				<!-- 收藏按钮 -->
				<view @tap="add_sc" class="action-btn-wrapper">
					<view class="action-btn" :class="info.is_info_sc ? 'active' : ''">
						<text class="action-icon">{{ info.is_info_sc ? '⭐' : '☆' }}</text>
						<view v-if="info.info_sc_count_this > 0" class="action-badge blue">
							{{ info.info_sc_count_this >= 99 ? '99+' : info.info_sc_count_this }}
						</view>
					</view>
				</view>

				<!-- 点赞按钮 -->
				<view @tap="add_zan" data-kkk="0" class="action-btn-wrapper">
					<view class="action-btn" :class="info.is_info_zan ? 'active' : ''">
						<text class="action-icon">{{ info.is_info_zan ? '❤️' : '🤍' }}</text>
						<view v-if="info.info_zan_count_this > 0" class="action-badge red">
							{{ info.info_zan_count_this >= 99 ? '99+' : info.info_zan_count_this }}
						</view>
					</view>
				</view>

				<!-- 转发按钮 -->
				<view v-if="version == 0 && (info.is_open == 1 || info.user_id == uid)" class="action-btn-wrapper">
					<view @tap="add_zhuanfa" class="action-btn">
						<text class="action-icon">📤</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 优化后的评论模态框 -->
		<view :class="'comment-modal ' + (isHuifuModalVisible ? 'show' : '')">
			<view class="comment-modal-content">
				<!-- 模态框头部 -->
				<view class="comment-modal-header">
					<view class="modal-title">💬 发表评论</view>
				</view>

				<!-- 标签页导航 -->
				<view class="comment-tabs-nav">
					<view :class="'tab-item ' + (current == 'tab1' ? 'active' : '')" @tap="handleChange"
						data-key="tab1">
						<view class="tab-text">图文</view>
					</view>
					<view v-if="copyright.hair_audio_arbor == 1"
						:class="'tab-item ' + (current == 'tab2' ? 'active' : '')" @tap="handleChange" data-key="tab2">
						<view class="tab-text">语音</view>
					</view>
					<view v-if="copyright.tribute_arbor == 1" :class="'tab-item ' + (current == 'tab3' ? 'active' : '')"
						@tap="handleChange" data-key="tab3">
						<view class="tab-text">礼物</view>
					</view>
				</view>
				<!-- 标签页内容区域 -->
				<view class="comment-tabs-content">
					<!-- 图文评论标签页 -->
					<view v-if="current == 'tab1'" class="tab-content text-tab">
						<view class="comment-input-section" v-if="isHuifuModalVisible">
							<textarea :focus="hui_focus" @tap="close_emoji" cursor-spacing="300rpx"
								class="comment-textarea" @input="get_text" :value="text" maxlength="300"
								placeholder="留下你精彩的评论吧..." />

							<view class="input-tools-bar">
								<view @tap="openEmoji" :class="'tool-btn emoji-btn ' + (emoji ? 'active' : '')">
									<text class="cicon-emoji-o"></text>
								</view>
								<view v-if="img_arr.length == 0" @tap="previewOneImage" class="tool-btn image-btn">
									<text class="cicon-pic-o"></text>
								</view>
							</view>
						</view>
						<!-- 表情选择器 -->
						<view v-if="emoji" class="emoji-picker">
							<swiper :indicator-dots="true" class="emoji-swiper">
								<swiper-item v-for="(emojis, e_index) in (info.expression)" :key="e_index">
									<view class="emoji-grid">
										<view @tap="set_emoji" :data-key="e_index" :data-index="n_index"
											class="emoji-item" v-for="(n_item, n_index) in (emojis)" :key="n_index">
											<image
												:src="(http_root) + 'addons/yl_welore/web/static/expression/' + (n_item)"
												class="emoji-image"></image>
										</view>
									</view>
								</swiper-item>
							</swiper>
						</view>
						<!-- 图片预览区域 -->
						<view class="image-preview-section" v-if="img_arr.length > 0">
							<view class="image-grid">
								<view class="image-item" v-for="(item, vido_index) in (img_arr)" :key="vido_index">
									<image :data-src="item" @tap="PreviewViewImage" :src="item" mode="aspectFill"
										class="preview-image">
									</image>
									<view class="image-remove-btn" @tap.stop.prevent="clearOneImage"
										:data-index="vido_index">
										<text class="cuIcon-close"></text>
									</view>
								</view>
							</view>
						</view>
						<!-- 自选昵称功能 -->
						<view v-if="copyright.engrave_arbor == 1 && version == 0" class="nickname-section">
							<view class="nickname-option nickname-btn" v-if="top_info == ''" @click="to_unlock">
								<text>自选昵称</text>
							</view>
							<view class="nickname-selected" v-if="top_info != ''">
								<image class="selected-avatar" :src="top_info.forgery_head"></image>
								<text class="selected-name">{{ top_info.forgery_name }}</text>
								<text @click="cancel_unlock" class="cancel-btn">✕ 取消</text>
							</view>
						</view>
					</view>
					<!-- 语音录制标签页 -->
					<view v-if="current == 'tab2'" class="tab-content voice-tab">
						<view class="voice-recorder-section">
							<!-- 音频播放器 -->
							<view class="audio-player-container">
								<view class="modal-audio-player-card">
									<view class="audio-controls">
										<text @tap="play_hf" v-if="(!recorder_play)"
											class="audio-btn play-btn cuIcon-videofill"></text>
										<text @tap="recorder_pause" v-if="recorder_play"
											class="audio-btn stop-btn cuIcon-stop"></text>
									</view>
									<view class="audio-visualizer">
										<text class="wave-bar" v-for="(item, index) in (8)" :key="index"></text>
									</view>
									<view class="audio-duration">{{ file_ss + '"' }}</view>
								</view>
							</view>

							<!-- 录音按钮 -->
							<view class="record-button-container">
								<view class="record-button" :class="star_recorder ? 'recording' : ''">
									<image @tap="touchStart" v-if="(!star_recorder)"
										:src="(http_root) + 'addons/yl_welore/web/static/applet_icon/star.png'"
										mode="aspectFill" class="record-icon"></image>
									<image @tap="touchEnd" v-if="star_recorder"
										:src="(http_root) + 'addons/yl_welore/web/static/applet_icon/stop.png'"
										mode="aspectFill" class="record-icon"></image>
								</view>
							</view>

							<view class="record-tip">
								🎤 点击开始录音（最长5分钟）
							</view>
						</view>
					</view>
					<!-- 礼物选择标签页 -->
					<view v-if="current == 'tab3'" class="tab-content gift-tab" id="liwu" :animation="animationDataLi">
						<view class="gift-section">
							<!-- 礼物列表 -->
							<scroll-view :scroll-x="true" class="gift-scroll">
								<view class="gift-list">
									<view :class="'gift-item ' + (li_index == dataListindex ? 'selected' : '')"
										:data-k="dataListindex" :data-id="item.id" @tap="liwu_index"
										v-for="(item, dataListindex) in (li_list)" :key="dataListindex">
										<image :src="item.tr_icon" class="gift-icon"></image>
										<view class="gift-name">{{ item.tr_name }}</view>
										<view class="gift-price">
											{{ item.tr_conch }}{{ $state.diy.currency }}
										</view>
									</view>
								</view>
							</scroll-view>

							<!-- 余额信息 -->
							<navigator url="/yl_welore/pages/packageC/user_details/index" hover-class="none">
								<view class="balance-info">
									<text class="balance-label">我的{{ $state.diy.currency }}</text>
									<image :src="$state.diy.currency_icon" class="currency-icon"></image>
									<text class="balance-amount">{{ user_info.conch }}</text>
								</view>
							</navigator>

							<!-- 打赏按钮 -->
							<button @tap="reward" class="reward-button">
								🎁 立即打赏
							</button>
						</view>
					</view>
					<view class="comment-modal-footer" :style="$state.isIphoneX ? 'padding-bottom: 30rpx;' : ''">
						<button @tap="no_huifu" class="footer-btn cancel-btn">
							关闭
						</button>
						<button @tap="submit" v-if="current != 'tab3'" class="footer-btn submit-btn">
							发布
						</button>
					</view>
				</view>

			</view>
		</view>
		<!-- 评论 -->
		<!-- 评论回复 -->
		<view v-if="reply_and_hui"
			style="width:100%;height:1160rpx;background-color:#ffffff;position:fixed;bottom:0px;border-top-left-radius:5px;border-top-right-radius:5px;z-index:10000;">
			<view style="text-align:center;">
				<view style="margin-top:15px;float:left;margin-left:42%;;float:left;">
					{{ get_reply_and_info.hui_count }}条回复
				</view>
				<!-- <image bindtap='no_huifu' src='../../../style/icon/cuo.png' style='width:20px;height:20px;float:right;margin-top:6px;margin-right:10px;'></image> -->
				<view class="flex justify-end" @tap="hideHuifuModal">
					<text class="cuIcon-close lg text-gray" style="margin: 10px;font-size: 25px;color: #000;"></text>
				</view>
			</view>
			<view style="clear:both;height:0"></view>
			<scroll-view @scrolltolower="page_get_reply" scroll-y style="height:940rpx;">
				<view>
					<view class="cu-list menu-avatar">
						<view class="cu-item">
							<view @tap="article_url" :data-id="get_reply_and_info.user_id" data-key="2"
								class="cu-avatar round eight"
								:style="'background-image:url(' + (get_reply_and_info.user_head_sculpture) + ');'">
								<view v-if="get_reply_and_info.user_id != 0" style="z-index:100"
									:class="'cu-tag badge ' + (get_reply_and_info.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')">
								</view>
								<image v-if="get_reply_and_info.user_id != 0" class="now_level"
									style="height: 50px;width: 50px;position: absolute;max-width:initial"
									:src="get_reply_and_info.avatar_frame"></image>
							</view>
							<view class="content flex-sub" style="left:140rpx;width:auto;">
								<view class="text-grey text-df">{{ get_reply_and_info.user_nick_name }}</view>
							</view>
						</view>
					</view>
					<view style="margin:0rpx 35rpx 10px 100rpx;word-break: break-all;">
						<rich-text :user-select="true" :nodes="get_reply_and_info.reply_content"></rich-text>
						<!-- <text user-select="true" style='word-break:break-all;font-size:14px;'>{{get_reply_and_info.reply_content}}</text> -->
						<view style="max-height:300px;overflow:hidden;margin-bottom:10px;">
							<image v-if="get_reply_and_info.image_part[0]" :src="get_reply_and_info.image_part[0]"
								:data-src="get_reply_and_info.image_part[0]" mode="widthFix" @tap="previewHuiAndImage"
								style="height:100px;width:100px;"></image>
						</view>
					</view>
					<view style="width:100%;border-top:0.5px solid #f9f9f9;"></view>
					<view style="padding-bottom:40px;">
						<view style="padding:10px 15px;font-size:14px;">全部评论</view>
						<view v-for="(item, index) in (get_reply_and_list)" :key="index">



							<view class="cu-list menu-avatar">
								<view class="cu-item">
									<view @tap="article_url" :data-id="item.user_id" data-key="2"
										class="cu-avatar round eight"
										:style="'background-image:url(' + (item.user_head_sculpture) + ');'">
										<view v-if="item.user_id != 0" style="z-index:100"
											:class="'cu-tag badge ' + (item.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')">
										</view>
										<image v-if="item.user_id == 0" class="now_level"
											style="height: 50px;width: 50px;position: absolute;max-width:initial"
											:src="item.avatar_frame">
										</image>
									</view>
									<view class="content flex-sub" style="left:132rpx;width: 420rpx;">
										<view class="text-black" style="font-size: 14px;">
											<text>{{ item.user_nick_name }}</text>
											<text v-if="item.reply_user_id != 0"
												style="word-break:break-all;font-size:14px;">
												<text class="cicon-play-arrow text-gray" style="margin:0px 5px;"></text>
												<text class="text-black">{{ item.hui_nick_name }}</text>
											</text>
										</view>
										<view class="text-gray text-sm">{{ item.duplex_time }}</view>
									</view>
									<view @tap="handleCancel3" data-type="2" :data-user_id="item.user_id"
										:data-id="item.id" :data-key="index" style="margin-right: 10px;"
										class="text-gray justify-end">
										<text class="cuIcon-more"
											style="font-size: 18px;vertical-align: middle;"></text>
									</view>
								</view>
							</view>






							<view style="margin:0rpx 35rpx 0rpx 100rpx;word-break: break-all;" @tap="get_reply_user"
								:data-id="item.id" :data-user_name="item.user_nick_name" :data-user_id="item.user_id">
								<rich-text :user-select="true" :nodes="item.duplex_content"></rich-text>
								<!-- <view style='word-break:break-all;font-size:15px;'>{{item.duplex_content}}</view> -->
								<view style="font-size:12px;margin-top:10px;">
									<text v-if="item.user_id == uid" @tap.stop.prevent="get_reply_user_del"
										:data-id="item.id" class="cu-tag bg-red radius sm">删除</text>
								</view>
							</view>



						</view>
						<!-- <view wx:if="{{di_get_reply}}" style='font-size:14px;text-align:center;padding:20px;color:#999999'>暂无更多评论</view> -->
						<view :class="'cu-load ' + (!di_get_reply ? 'loading' : 'over')"></view>
					</view>
				</view>
			</scroll-view>
			<view :class="'cu-bar foot input ' + (PhShow ? 'iphoneX-height-and' : 'iphoneX-height')">
				<block v-if="copyright.engrave_arbor == 1">
					<view @tap="to_unlock" class="cu-avatar round"
						:style="'background-image:url(' + (top_info.forgery_head) + ');'">
					</view>
					<view class="action" v-if="top_info != '' && copyright.engrave_arbor == 1">
						<text class="text_num_1" style="color:#999999;font-size:12px;max-width: 50px;">{{
							top_info.forgery_name
						}}</text>
						<text @tap="cancel_unlock" class="cuIcon-roundclosefill text-gray"
							style="font-size:20px;margin-left:8px;"></text>
					</view>
				</block>
				<input :focus="focus" :value="replyText" @focus="ph_focus" @input="handleReplyInput"
					:placeholder="placeholder_reply" class="" maxlength="300" cursor-spacing="10" />
				<view class="action" @tap="openPhEmoji">
					<text class="cicon-emoji-o text-black" style="font-size: 25px;"></text>
				</view>
				<button @tap="add_reply_and_hui" class="cu-btn bg-green shadow">回复</button>
			</view>
			<view v-if="PhShow" style="width: 100%;height: 400rpx;background-color: #F3F3F3;position: fixed;bottom: 0;">
				<swiper :indicator-dots="true" style="height:400rpx;">
					<block v-for="(emojis, t_index) in (info.expression)" :key="t_index">





						<swiper-item>
							<view class="grid col-9 margin-bottom text-center" style="padding-top: 10px;">
								<view @tap="set_ph_emoji" :data-key="t_index" :data-index="n_index"
									style="margin: 5px 0px;" v-for="(n_item, n_index) in (emojis)" :key="n_index">




									<image :src="(http_root) + 'addons/yl_welore/web/static/expression/' + (n_item)"
										style="width: 60rpx;height: 60rpx;"></image>




								</view>
							</view>
						</swiper-item>





					</block>
				</swiper>
			</view>
		</view>
		<!-- 评论回复 -->
		<view v-if="reply_and_hui" @touchmove.stop.prevent="preventTouchMove"
			style="width:100%;height:100%;position:fixed;background-color:#999;z-index:9999;top:0;left:0;opacity:0.3;">
		</view>
		<view :class="'cu-modal ' + (set_img_quan ? 'show' : '')" @touchmove.stop.prevent="true">
			<view class="cu-dialog" style="width:300px;background-color: #ffffff;">
				<view class="bg-img">
					<scroll-view scroll-y v-if="set_img_quan" class="modal-content" style="width:100%;">
						<view id="canvas-container">
							<canvas canvas-id="myCanvas"
								style="width:100%;background-color:#ffffff;width: 300px;height: 310px;"></canvas>
						</view>
					</scroll-view>
				</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn line-green text-green" @tap="no_set_img_quan">取消</button>
						<button class="cu-btn bg-green margin-left" @tap="saveShareImg">保存</button>
					</view>
				</view>
			</view>
		</view>
		<!-- 优化后的分享弹窗 -->
		<view v-if="zhuanfa" class="modern-share-modal" @tap="zhuanfa = false">
			<view class="share-modal-content" @tap.stop>
				<!-- 弹窗头部 -->
				<view class="share-modal-header">
					<view class="share-title">
						<text class="share-icon">📤</text>
						<text class="share-text">分享到</text>
					</view>
					<view class="share-close" @tap="zhuanfa = false">
						<text class="cuIcon-close"></text>
					</view>
				</view>

				<!-- 分享选项 -->
				<view class="share-options">
					<view class="share-option-item">
						<button hover-class="none" open-type="share" class="share-btn wechat-btn">
							<view class="share-btn-icon">
								<text class="share-emoji">💬</text>
							</view>
							<text class="share-btn-text">微信好友</text>
						</button>
					</view>

					<view class="share-option-item">
						<view @tap="set_img_quan_tap" class="share-btn moments-btn">
							<view class="share-btn-icon">
								<text class="share-emoji">🌟</text>
							</view>
							<text class="share-btn-text">朋友圈</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="cu-load load-modal" v-if="loadModal">
			<view class="gray-text">上传中...</view>
		</view>
		<!-- 帖子操作 -->
		<view :class="'cu-modal bottom-modal ' + (huoqu_tie ? 'show' : '')">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">操作</view>
				</view>
				<view v-if="visible" class="padding-sm">
					<view class="cf padding-sm">
						<view @tap="tiezi_select" :data-index="a_index"
							class="bg-yellow radius fl padding-sm margin-sm text-white"
							v-for="(item, a_index) in actions" :key="a_index">
							{{ item.name }}
						</view>
					</view>
				</view>
				<view v-if="jubao && huoqu_tie" class="padding">
					<textarea style="min-height:4em;text-align:left;" @input="get_jubao_text" :auto-height="true"
						value="" class="weui-textarea" maxlength="300" placeholder="请具体说明问题，我们将尽快处理" />
				</view>
				<view v-if="tz_del_msg && huoqu_tie" class="padding">
					<textarea style="min-height:4em;text-align:left;" @input="handleReasonInput" :auto-height="true"
						value="" class="weui-textarea" maxlength="300" placeholder="请具体说明问题，我们将尽快处理" />
				</view>
				<view v-if="jinyan && huoqu_tie" class="grid col-3 padding-sm">
					<view class="padding-sm" @tap="select_jy" :data-index="t_index" v-for="(item, t_index) in actions2"
						:key="t_index">
						<view class="bg-red padding-sm radius text-center shadow-blur">
							<view class="text-lg">禁言</view>
							<view class="margin-top-sm text-Abc">{{ item.name }}</view>
							<text v-if="t_index == jinyan_index" class="cuIcon-roundcheckfill text-red"
								style="position: absolute;top: -10px;right: -10px;font-size: 30px;"></text>
						</view>
					</view>
					<textarea @input="handleReasonInput"
						style="height:5em;width:100%;padding:10px;font-size:14px;text-align: left;"
						placeholder="请填写禁言理由" />
				</view>
				<view class="cu-bar bg-white justify-center">
					<view v-if="visible" @tap="hideModal">取消</view>
					<view v-if="jubao" @tap="jubao_submit">确定</view>
					<view v-if="tz_del_msg" @tap="del_tz_do">确定</view>
					<view v-if="jinyan" @tap="do_banned_user">确定</view>
				</view>
			</view>
		</view>
		<!-- 帖子操作 -->
		<!-- 回复操作 -->
		<view :class="'cu-modal bottom-modal ' + (huoqu_ping ? 'show' : '')" style="z-index: 300000;">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">操作</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view v-if="visible1" class="padding">
					<view class="cf padding-sm">
						<view @tap="huifu_select" :data-index="c_index"
							class="bg-yellow radius fl padding-sm margin-sm text-white"
							v-for="(item, c_index) in (actions1)" :key="c_index">
							{{ item.name }}
						</view>
					</view>
				</view>
				<view v-if="jubao && huoqu_ping" class="padding">
					<textarea style="min-height:4em;text-align:left;" @input="get_jubao_text" :auto-height="true"
						value="" class="weui-textarea" maxlength="300" placeholder="请具体说明问题，我们将尽快处理" />
				</view>
				<view v-if="del_msg && huoqu_ping" class="padding">
					<textarea style="min-height:4em;text-align:left;" @input="handleReasonInput" :auto-height="true"
						value="" class="weui-textarea" maxlength="300" placeholder="请具体说明问题，我们将尽快处理" />
				</view>
				<view v-if="jinyan && huoqu_ping" class="grid col-3 padding-sm">
					<view class="padding-sm" @tap="select_jy" :data-index="t_index" v-for="(item, t_index) in actions2"
						:key="t_index">
						<view class="bg-red padding-sm radius text-center shadow-blur">
							<view class="text-lg">禁言</view>
							<view class="margin-top-sm text-Abc">{{ item.name }}</view>
							<text v-if="t_index == jinyan_index" class="cuIcon-roundcheckfill text-red"
								style="position: absolute;top: -10px;right: -10px;font-size: 30px;"></text>
						</view>
					</view>
					<textarea @input="handleReasonInput"
						style="height:5em;width:100%;padding:10px;font-size:14px;text-align: left;"
						placeholder="请填写禁言理由" />
				</view>
				<view class="cu-bar bg-white justify-center" style="height: 120rpx;padding-bottom: 20rpx;">
					<view v-if="visible1" @tap="hideModal">取消</view>
					<view v-if="jubao" @tap="jubao_submit">确定</view>
					<view v-if="del_msg" @tap="del_do">确定</view>
					<view v-if="jinyan" @tap="do_banned_user">确定</view>
				</view>
			</view>
			<!-- 回复操作 -->


		</view>
		<view :class="'cu-modal ' + (purchase_paper_mod ? 'show' : '')" style="z-index:10005">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">
						<text v-if="info.is_buy == 0">提示</text>
						<text v-if="info.is_buy == 1">兑换内容</text>
						<text v-if="info.is_buy == 2">兑换文件</text>
						<text v-if="info.is_buy == 3">兑换内容</text>
					</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding" v-if="info.is_buy != 0">
					<view class="modal-content">
						<view style="font-size:14px;text-align:center;">
							使用{{ info.buy_price_type == 0 ? copyright.currency : copyright.confer }}兑换
						</view>
						<view style="font-size:30px;text-align:center;margin:15px;">
							-{{ info.buy_price }}
						</view>
					</view>
					<navigator url="/yl_welore/pages/packageC/user_details/index" hover-class="none">
						<view class="modal-content" style="position:relative;padding: 5px 0px;">
							<image :src="$state.diy.currency_icon"
								style="width:25px;height:25px;vertical-align:middle;">
							</image>
							<text v-if="info.buy_price_type == 0"
								style="vertical-align: middle;font-size:14px;margin-left:10px;color:#717470;">我的{{
									copyright.currency
								}}：{{ user_info.conch }}</text>
							<text v-if="info.buy_price_type == 1"
								style="vertical-align: middle;font-size:14px;margin-left:10px;color:#717470;">我的{{
									copyright.confer
								}}：{{ user_info.fraction }}</text>
						</view>
					</navigator>
				</view>
				<view class="padding bg-white" v-if="info.is_buy == 0">如果您要查看本帖隐藏内容请回复</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
						<button v-if="info.is_buy != 0" class="cu-btn bg-green margin-left" @tap="do_paper_money">
							确定
						</button>
					</view>
				</view>
			</view>
		</view>
		<view :class="'cu-modal ' + (visible2 ? 'show' : '')">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">提示</view>
					<view class="action" @tap="handleClose">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding">需要使用您的录音功能，是否允许？</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn line-green text-green" @tap="handleClose">取消</button>
						<button class="cu-btn bg-green margin-left" @tap="handleOk">确定</button>
					</view>
				</view>
			</view>
		</view>
		<!-- 优化后的点评详情模态框 -->
		<view :class="'modern-comment-detail-modal ' + (CommentInfoMod ? 'show' : '')">
			<view class="modern-comment-detail-dialog">
				<!-- 模态框头部 -->
				<view class="comment-detail-header">
					<view class="header-content">
						<view class="header-icon">📝</view>
						<view class="header-title">点评详情</view>
					</view>
					<view class="header-close" @tap="hideModal">
						<text class="cuIcon-close"></text>
					</view>
				</view>

				<!-- 用户信息区域 -->
				<view class="comment-user-section" v-if="CommentInfo.user_head_sculpture">
					<view class="user-avatar-wrapper">
						<image class="user-avatar" :src="CommentInfo.user_head_sculpture" mode="aspectFill"></image>
					</view>
					<view class="user-info">
						<view class="user-name">{{ CommentInfo.user_nick_name || '匿名用户' }}</view>
					</view>
				</view>

				<!-- 评分区域 -->
				<view class="comment-rating-section">
					<view class="rating-stars-wrapper">
						<text class="detail-rating-star" :class="CommentInfo.assess_score > ss_index ? 'active' : ''"
							v-for="(item, ss_index) in (5)" :key="ss_index">⭐</text>
					</view>
				</view>

				<!-- 评语内容区域 -->
				<view class="comment-content-section">
					<view class="content-label">
						<text class="content-icon">💭</text>
						<text class="content-title">评语内容</text>
					</view>
					<view class="content-text">
						{{ CommentInfo.assess_content }}
					</view>
				</view>

				<!-- 操作按钮区域 -->
				<view class="comment-actions-section" v-if="info.admin == 1">
					<view class="action-buttons">
						<view @tap="DelComment" class="action-btn delete-btn">
							<text class="btn-icon">🗑️</text>
							<text class="btn-text">删除</text>
						</view>
						<!-- <view @tap="hideModal" class="action-btn confirm-btn">
							<text class="btn-icon">✅</text>
							<text class="btn-text">确定</text>
						</view> -->
					</view>
				</view>
			</view>
		</view>
		<!-- 优化后的点评模态框 -->
		<view :class="'modern-comment-modal ' + (modalComment ? 'show' : '')">
			<view class="modern-comment-dialog">
				<!-- 模态框头部 -->
				<view class="modern-comment-header">
					<view class="header-content">
						<view class="header-icon">⭐</view>
						<view class="header-title-n">内容点评</view>
					</view>
					<view class="header-close" @tap="hideModal">
						<text class="cuIcon-close"></text>
					</view>
				</view>

				<!-- 评分卡片 -->
				<view class="rating-card">
					<view class="rating-title">{{ CommentName[SlectStar] }}</view>
					<view class="rating-stars">
						<text @tap="CommentDo" :data-index="ss_index"
							:class="'rating-star ' + (ss_index <= SlectStar ? 'active' : '')"
							v-for="(item, ss_index) in (5)" :key="ss_index">⭐</text>
					</view>
					<view class="rating-description">点击星星进行评分</view>
				</view>

				<!-- 评语输入卡片 -->
				<view class="comment-input-card">
					<view class="input-label">
						<text class="input-icon">💭</text>
						<text class="input-title">评语内容</text>
					</view>
					<view class="input-wrapper">
						<textarea v-if="modalComment" :value="CommentText" class="modern-textarea" maxlength="300"
							:disabled="(!modalComment)" @input="textareaBInput" placeholder="分享您的真实感受，帮助其他用户做出更好的选择...">
						</textarea>
					</view>
				</view>

				<!-- 选项卡片 -->
				<view class="options-card">
					<view class="option-item" @tap="check_words" data-key="1">
						<view class="option-content">
							<text class="option-icon">👁️</text>
							<text class="option-text">公开显示</text>
						</view>
						<view class="modern-switch" :class="Mdisplay ? 'active' : ''">
							<view class="switch-handle"></view>
						</view>
					</view>

					<view class="option-item" @tap="check_words" data-key="2">
						<view class="option-content">
							<text class="option-icon">💾</text>
							<text class="option-text">保存为常用语</text>
						</view>
						<view class="modern-switch" :class="Words ? 'active' : ''">
							<view class="switch-handle"></view>
						</view>
					</view>

					<view class="option-item" @tap="openRightMode">
						<view class="option-content">
							<text class="option-icon">📝</text>
							<text class="option-text">选择常用语</text>
						</view>
						<view class="option-arrow">
							<text class="cuIcon-right"></text>
						</view>
					</view>
				</view>

				<!-- 提交按钮 -->
				<view class="submit-section">
					<view @tap="SubmitComment" class="modern-submit-btn">
						<text class="submit-icon">✅</text>
						<text class="submit-text">提交点评</text>
					</view>
				</view>
			</view>
		</view>
		<view style="z-index: 13000;" :class="'cu-modal drawer-modal justify-end ' + (RightMode ? 'show' : '')"
			@tap="hideRightModal" @touchmove.stop.prevent="stopEvent">
			<scroll-view :scroll-y="true" class="cu-dialog basis-lg bg-white" catchtap
				style="top:166rpx;height:calc(90vh);padding: 10px;">
				<block v-for="(item, y_index) in review_terms" :key="y_index">
					<view @tap="InsText" :data-index="y_index" class="text_num_3">
						{{ item.common_content }}
					</view>
					<view class="bg-gray" style="height: 1px;width: 100%;margin: 15px 0px;"></view>
				</block>
			</scroll-view>
		</view>
		<login id="login" @checkPhoen="check_user_login = false;" :check_user_login="check_user_login"></login>
		<phone id="phone" @close_phone_modal="check_phone_close" :check_phone="check_phone_show"></phone>
		<privacy />
	</view>
</template>

<script>
var app = getApp();
var http = require("@/yl_welore/util/http.js");
import login from "@/yl_welore/util/user_login/login";
import phone from "@/yl_welore/util/user_phone/phone";
import util from '@/yl_welore/util/data.js';
var recorderManager_hf = uni.getRecorderManager();
const innerAudioContext = uni.getBackgroundAudioManager();
const Mp = uni.createInnerAudioContext();
innerAudioContext.obeyMuteSwitch = false;
var options_hf = {
	duration: 300000, //指定录音的时长，单位 ms
	sampleRate: 16000, //采样率
	numberOfChannels: 1, //录音通道数
	encodeBitRate: 96000, //编码码率
	format: 'mp3', //音频格式，有效值 aac/mp3
	frameSize: 50, //指定帧大小，单位 KB
}
var fsm = uni.getFileSystemManager();
var recordTimeInterval_a = '';

var show_type = 'all';
export default {
	components: {
		login,
		phone
	},
	/**
	 * 页面的初始数据
	 */
	data() {
		return {
			tz_del_msg: false,
			refresh_user_info: false,
			isIphoneX: app.globalData.isIpx,
			http_root: app.globalData.http_root,
			top_info: '',
			page: 1,
			voi_item: 0,
			check_img: '',
			painting: '',
			set_img_quan: false,
			visible: false, //帖子操作
			visible1: false, //回复操作
			zhuanfa: false,
			jinyan_index: -1,
			jinyan: false,
			actions_index: [],
			actions: [],
			user_time_msg: false,
			actions1: [],
			actions2: [],
			info: {},
			del_msg: false,
			copyright: {},
			current: 'tab1',
			current_h: 'h1',
			jubao: false,
			huifu_zan: false,
			huifu_list: [],
			animationData: {},
			img_arr: [], //图片
			img_botton: true,
			file: '',
			file_ss: 0,
			text: '',
			user_time_check: false,
			jubao_text: '',
			di_msg: false,
			del_mod: false,
			info_zan: false,
			info_zan_count: 0,
			info_sc: false,
			info_sc_count: 0,
			select: true,
			reasonText: '',
			li_number: 1,
			li_list: [],
			user_info: {},
			qrCode: "", //需要https图片路径
			show: true,
			reply_and_hui: false, //回复评论
			get_reply_and_info: {},
			get_reply_and_list: [],
			get_reply_and_text: '',
			get_reply_and_id: 0,
			placeholder_reply: '写评论',
			get_reply_and_page: 1,
			di_get_reply: false,
			get_reply_and_user_id: 0,
			focus: false,
			version: 1,
			admin: 0,
			isHuifuModalVisible: false,
			ios: false,
			info_type: 0,
			level_images: {},
			level_images_hui: [],
			loadModal: false,
			huoqu_tie: false,
			huoqu_ping: false,
			check_user_login: false,
			check_phone_show: false,
			time_page: 1,
			user_time: [],
			li_index: null,
			scene: 1001,
			vd_width: 0,
			vd_height: 0,
			dian_index: -1,
			scope_record: true, //是否授权录音
			//订阅
			purchase_paper_mod: false,
			//插件
			open_user_phone: false,
			open_user_wangpan: false,
			//是否在录音
			star_recorder: false,
			recorder_play: false,
			//点评
			CommentInfo: '',
			CommentText: '',
			modalComment: false,
			CommentName: ['差', '较差', '一般', '较好', '好'],
			SlectStar: 4,
			RightMode: false,
			CommentInfoMod: false,
			Mdisplay: false,
			Words: false,
			review_terms: [],
			open_user_dianping: false,
			//表情
			emoji: false,
			hui_focus: false,
			PhShow: false,
			del_type: 1,
		}
	},

	onLoad(options) {
		uni.showLoading({
			title: '详情加载中...',
			mask: true,
		});
		uni.showShareMenu({
			menus: ['shareAppMessage', 'shareTimeline']
		})
		var scene = decodeURIComponent(options.scene);
		var op = uni.getLaunchOptionsSync();
		console.log(scene);
		if (scene == "undefined") {
			//console.log(1);
			this.id = options.id;
			this.info_type = options.type;
		} else {
			//console.log(2);
			var ssp = scene.split('-');
			this.id = ssp[0];
			this.info_type = ssp[1];
		}
		this.huifu_list = [];
		this.scene = op.scene;
		if (op.scene == 1154) {
			this.get_article_info();
		} else {
			this.doIt();
			var name_card = app.globalData.getCache('name_card');
			if (name_card != '') {
				this.get_one_name_card(name_card);
			}
		}
	},
	onShow() {
		show_type = 'all';
		var over_time = app.globalData.getCache("over_time");
		var dd = uni.getStorageSync('is_diy');
		var this_time = parseInt(+new Date / 1000);
		if (Object.keys(dd).length === 0 && over_time > this_time) {
			this.get_diy();
		}
		var that = this;
		//获取系统信息
		uni.getSystemInfo({
			success(res) {
				console.log(res);
				var copyright = that.copyright;
				if (res.platform == "ios" && copyright.ios_pay_arbor == 0) {
					that.ios = false;
				}
				if (res.platform == "ios" && copyright.ios_pay_arbor == 1) {
					that.ios = true;
				}
				if (res.platform != "ios") {
					that.ios = true;
				}
			}
		})
		if (this.refresh_user_info) {
			this.get_user_info();
		}
		var phone = app.globalData.__PlugUnitScreen('b738745f2187839767e8a5aa93bc336e');
		var wangpan = app.globalData.__PlugUnitScreen('eae4f6cbbecdb89ea5a61ec602ec7000');
		console.log(wangpan);
		var dianping = app.globalData.__PlugUnitScreen('6f7f2e87a00f58c6d00817b72dd332c9');
		this.open_user_phone = phone;
		this.open_user_wangpan = wangpan;
		this.open_user_dianping = dianping;
		var name_card = app.globalData.getCache('name_card');
		if (name_card) {
			this.top_info = name_card;
		}
		console.log('----------', name_card);
	},
	onPullDownRefresh() {
		//uni.showNavigationBarLoading() //在标题栏中显示加载
		//模拟加载
		setTimeout(() => {
			uni.hideNavigationBarLoading() //完成停止加载
			uni.stopPullDownRefresh() //停止下拉刷新
		}, 1500);
		uni.showLoading({
			title: '详情加载中...',
			mask: true,
		});
		this.huifu_list = [];
		this.page = 1;
		this.get_article_info();

	},
	onShareTimeline() {
		var title = this.info['study_title'] == '' ? this.info['study_content'] : this.info['study_title'];
		var result = title.replace(/<[^>]*>/g, '');
		var img;
		this.user_forward(this.id);
		console.log(this.info);
		if (this.info.forward_img) {
			img = this.info.forward_img[0];
		} else {
			if (this.info['image_part'][0] != '') {
				img = this.info['image_part'][0];
			}
		}
		return {
			title: result,
			path: '/yl_welore/pages/packageA/article/index?id=' + this.id + '&type=' + this.info_type,
			imageUrl: img
		}
	},
	onShareAppMessage() {
		var forward = app.globalData.store.getState().copyright.forward;
		var that = this;

		var title = this.info['study_title'] == '' ? this.info['study_content'] : this.info['study_title'];


		var result = title.replace(/<[^>]*>/g, '');
		console.log(result);
		that.user_forward(that.id);
		if (forward['whether_open'] == 1) {
			return {
				title: forward.title,
				path: '/yl_welore/pages/packageA/article/index?id=' + this.id + '&type=' + this.info_type,
				imageUrl: forward.reis_img,
			}
		} else {
			var img;
			console.log(this.info);
			if (this.info.forward_img) {
				img = this.info.forward_img[0];
			} else {
				if (this.info['image_part'][0] != '') {
					img = this.info['image_part'][0];
				}
			}
			return {
				title: result,
				path: '/yl_welore/pages/packageA/article/index?id=' + this.id + '&type=' + this.info_type,
				imageUrl: img,
			}
		}
	},
	methods: {
		check_phone_close() {
			this.check_phone_show = false;
		},
		// 检查选项是否被选中
		isSelected(optionId) {
			if (!this.info.vo_id || !Array.isArray(this.info.vo_id)) {
				return false;
			}
			return this.info.vo_id.indexOf(optionId) !== -1;
		},
		showHuifuModal() {
			this.isHuifuModalVisible = true;
		},
		hideHuifuModal() {
			this.isHuifuModalVisible = false;
		},
		getOpc() {
			var that = this;
			uni.authorize({
				scope: 'scope.writePhotosAlbum',
				success(res) {
					that.xinDownVideo();
				},
				fail(res) {
					console.log(res);
					uni.showModal({
						title: '提示',
						content: '检测到您没打开相册保存权限，是否去设置打开？',
						success(res) {
							if (res.confirm) {
								console.log('用户点击确定')
								uni.openSetting({
									success: (res) => {
										console.log(res)
									},
								})
							} else if (res.cancel) { }
						},
					})
				}
			})
		},
		xinDownVideo() {
			uni.showLoading({
				title: '下载中...',
			})
			var url = this.info.study_video;
			const downloadTask = uni.downloadFile({
				url: url,
				success(res) {
					console.log(res);
					uni.saveVideoToPhotosAlbum({
						filePath: res.tempFilePath,
						success(res) {
							console.log(res);
							uni.showToast({
								title: '保存成功',
							})
						},
						fail(res) {
							//关闭加载提示
							uni.hideLoading();
							if ("saveVideoToPhotosAlbum:fail auth deny" == res.errMsg ||
								"saveVideoToPhotosAlbum:fail:auth deny" == res.errMsg ||
								"saveVideoToPhotosAlbum:fail:auth denied" == res.errMsg ||
								"saveVideoToPhotosAlbum:fail authorize no response" == res.errMsg) {
								uni.showModal({
									title: '提示',
									content: '检测到您没打开相册保存权限，是否去设置打开？',
									success(res) {
										if (res.confirm) {
											console.log('用户点击确定')
											uni.openSetting({
												success: (res) => {
													console.log(res)
												},
											})
										} else if (res.cancel) { }
									},
								})
							} else {
								console.log(res.errMsg);
								uni.showToast({
									// title: '保存失败，请联系客服反馈',
									title: res.errMsg,
									icon: 'none'
								})
							}
						},
					})
				},
				fail(res) {
					uni.hideLoading();
					console.log(res);
					uni.showModal({
						title: '提示',
						content: res.errMsg,
					})
				}
			})

			downloadTask.onProgressUpdate((res) => {
				uni.showLoading({
					title: `下载进度` + res.progress + `%`,
				})
				if (res.progress >= 100) {
					uni.showToast({
						title: '下载成功',
					})
					uni.hideLoading()
				}
			})

		},
		DelComment() {
			var that = this;
			uni.showModal({
				title: '提示',
				content: '确定要删除这个点评吗？',
				success(res) {
					if (res.confirm) {
						that.DelCommentDo();
					}
				}
			})
		},
		/**
		 * 删除
		 */
		DelCommentDo() {
			var info = this.CommentInfo;
			uni.showLoading({
				title: '提交中...',
			})
			var b = app.globalData.api_root + 'Polls/del_comment';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.com_id = info.id;
			params.paper_id = this.id;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					uni.showToast({
						title: res.data.msg,
						icon: 'none',
						duration: 2000
					})
					if (res.data.status == 'success') {
						that.CommentInfoMod = false;
						that.CommentInfo = '';
					}
					that.get_article_info();
					uni.hideLoading();
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 查看更多
		 */
		open_more() {
			uni.navigateTo({
				url: '/yl_welore/pages/packageA/article_review/index?id=' + this.id
			})
		},
		ph_focus() {
			this.PhShow = false;
		},
		set_ph_emoji(d) {
			var index = d.currentTarget.dataset.index;
			var t_index = d.currentTarget.dataset.key;
			var str = this.info['expression'][t_index][index];
			var k = str.split('.')[0];
			console.log(k);
			this.get_reply_and_text = this.get_reply_and_text + '[#:' + k + ']';
		},
		openPhEmoji() {
			this.PhShow = !this.PhShow;
			this.focus = false;
		},
		close_emoji() {
			this.emoji = false;
			this.hui_focus = true;
		},
		set_emoji(d) {
			var index = d.currentTarget.dataset.index;
			var t_index = d.currentTarget.dataset.key;
			var str = this.info['expression'][t_index][index];
			var k = str.split('.')[0];
			console.log(k);
			this.text = this.text + '[#:' + k + ']';
		},
		openEmoji() {
			this.emoji = !this.emoji;
			this.hui_focus = false;
		},
		check_words(d) {
			console.log(d);
			var key = d.currentTarget.dataset.key;
			if (key == 1) {
				this.Mdisplay = !this.Mdisplay;
			} else {
				this.Words = !this.Words;
			}
		},
		SubmitComment() {
			uni.showLoading({
				title: '提交中...',
			})
			var b = app.globalData.api_root + 'Polls/paper_review';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.paper_id = this.info.id;
			params.is_show = this.Mdisplay ? 1 : 0;
			params.words = this.Words ? 1 : 0;
			params.comment_text = this.CommentText;
			params.star = this.SlectStar;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == 'error') {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							showCancel: false,
							success: (res) => { }
						})
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.hideModal();
						that.get_article_info();
					}
					uni.hideLoading();
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		open_comment_info(d) {
			console.log(d);
			var index = d.currentTarget.dataset.index;
			this.CommentInfoMod = true;
			this.select = false;
			this.CommentInfo = this.info.review_score[index];
		},
		InsText(d) {
			var index = d.currentTarget.dataset.index;
			this.CommentText = this.review_terms[index].common_content;
		},
		textareaBInput(e) {
			this.CommentText = e.detail.value;
		},
		openRightMode() {
			this.RightMode = true;
			this.select = false;
		},
		hideRightModal(e) {
			this.RightMode = false;
		},
		CommentDo(d) {
			this.SlectStar = d.currentTarget.dataset.index;
		},
		open_comment() {
			this.modalComment = true;
			this.select = false;
		},
		open_phone(d) {
			uni.makePhoneCall({
				phoneNumber: this.info['call_phone']
			})
		},
		open_file() {
			//var e = app.globalData.globalData.getCache("userinfo");
			// if (e.id == this.info.user_id) {
			//   //跳转网盘
			//   uni.navigateTo({
			//     url: '/yl_welore/pages/packageF/netdisc/index'
			//   })
			//   return;
			// }
			var that = this;

			if (this.info.is_buy == 0 || this.info.check_look == 1) {
				//保存网盘
				uni.showModal({
					title: '保存提醒',
					content: '是否保存到网盘？',
					success(res) {
						if (res.confirm) {
							that.inst_file_my_ng();
						}
					}
				})

				return;
			}
			this.purchase_paper_mod = true;
		},
		inst_file_my_ng() {
			var b = app.globalData.api_root + 'Storage/inst_file_my_ng';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.info.id;
			http.POST(b, {
				params: params,
				success: (res) => {
					if (res.data.status == 'error') {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							showCancel: false,
							success: (res) => { }
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							confirmText: '前往网盘',
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/yl_welore/pages/packageF/netdisc/index',
									})
								}
							}
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		linktap(e) {
			console.log(e);
			var key = e.jump_type;
			if (key == 0) {
				return;
			} else if (key == 1) {
				uni.navigateToMiniProgram({
					appId: e.data_appid,
					path: e.href
				})
			} else if (key == 2) {
				uni.navigateTo({
					url: '/yl_welore/pages/web/index?url=' + encodeURIComponent(e.href)
				})
			} else {
				uni.setClipboardData({
					data: e.href,
					success(res) {

					}
				})
			}
		},
		/**
		 * 点击话题
		 */
		gambit_list(d) {
			// var e = app.globalData.getCache("userinfo");
			// var copyright = getApp().store.getState().copyright;
			// if (e.tourist == 1 && copyright.warrant_arbor == 1) {
			// 	this.check_phone_show = true;
			// 	return;
			// }
			var id = d.currentTarget.dataset.id;
			uni.navigateTo({
				url: '/yl_welore/pages/gambit/index?id=' + id
			})
		},
		cancel_unlock() {
			this.top_info = '';
		},
		to_unlock() {
			uni.navigateTo({
				url: '/yl_welore/pages/packageD/nameplate/index?type=add',
			})
		},
		//投票
		vote_do(item) {
			var index = this.dian_index;
			if (this.info.vo_id.length == 0) {
				uni.showToast({
					title: '请选择选项',
					icon: 'none',
					duration: 2000
				})
				return;
			}
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.paper_id = this.info.id;
			params.vo_id = this.agree(this.info.vo_id);
			var b = app.globalData.api_root + 'Polls/vote_do';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						this.info.is_vo_check = 1;
						this.info.vo_count = this.info.vo_count + 1;
						this.info.vo[index].voters = this.info.vo[index].voters + 1;
					} else {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							showCancel: false,
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		agree(rows) {
			var ids = [];
			for (var i = 0; i < rows.length; i++) {
				var signAgainReq = new Object();
				signAgainReq.pv_id = rows[i];
				ids.push(signAgainReq);
			}
			return JSON.stringify(ids);
		},
		//点击选项
		dian_option(item) {
			var index = item.currentTarget.dataset.index;
			this.dian_index = index;
			var info = this.info;
			if (info.is_vo_check > 0) {
				return;
			}
			var vo_id = item.currentTarget.dataset.id;
			var vo_id_list = this.info.vo_id;
			if (info['study_type'] == 4) { //单选
				if (vo_id == info['vo_id'][0]) {
					this.info.vo_id = [];
					return;
				}
				this.info.vo_id = [vo_id];
			} else {
				var vo_id_index = vo_id_list.indexOf(vo_id);
				if (vo_id_list.indexOf(vo_id) != -1) {
					var vo_id_list = info.vo_id;
					vo_id_list.splice(vo_id_index, 1);
					this.info.vo_id = vo_id_list;
					return;
				} else {
					var vo_id_list = info.vo_id;
					vo_id_list.push(vo_id);
					this.info.vo_id = vo_id_list;
				}
			}

		},
		eventGetImage(c) {
			console.log(c);
		},
		scrolltolower() {
			this.time_page = this.time_page + 1;
			this.get_user_time();
		},
		set_user_time() {
			console.log(this.user_time_check);
			if (this.user_time_check == true) {
				this.user_time_check = false;
				return;
			}
			this.user_time_check = true;
			this.user_time = [];
			this.time_page = 1;
			this.get_user_time();
		},
		//获取详情
		get_user_time() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.uid = e.uid;
			params.time_page = that.time_page;
			params.paper_id = that.id;
			params.at_id = that.info['brisk_team']['id'];
			var b = app.globalData.api_root + 'Activity/get_user_time';
			var allMsg = that.user_time;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.length == 0 || allMsg.length < 10) {
						that.user_time_msg = true;
					}
					for (var i = 0; i < res.data.length; i++) {
						allMsg.push(res.data[i]);
					}
					that.user_time = allMsg;
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		add_goumai() {
			if (this.info.study_type == 3) {
				var brisk_team = this.info.brisk_team;
				var n = parseInt(+new Date / 1000);
				//结束时间
				if (n > parseInt(brisk_team['end_time_y'])) {
					uni.showModal({
						title: '提示',
						content: '该活动已经过期',
					})
					return;
				}
			}
			this.purchase_paper_mod = true;

		},
		article_url(d) {
			var key = d.currentTarget.dataset.key;
			if (key == 1) { //发帖人
				if (this.info.user_id == 0) {
					uni.showToast({
						title: '身份已隐藏',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				uni.navigateTo({
					url: '/yl_welore/pages/packageB/my_home/index?id=' + this.info.user_id,
				})
				return;
			}
			if (key == 2) { //其他
				var id = d.currentTarget.dataset.id;
				if (id == 0) {
					uni.showToast({
						title: '身份已隐藏',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				uni.navigateTo({
					url: '/yl_welore/pages/packageB/my_home/index?id=' + id,
				})
				return;
			}

		},
		//参加活动
		participation_activities() {
			var that = this;
			var checkLogin = app.globalData.checkPhoneLogin(0);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}

			console.log(this.info);
			if (this.info.check_look == 0 && this.info.is_buy != 0) {
				that.add_goumai();
				return;
			}

			uni.showModal({
				title: '活动提醒',
				content: '确定参加活动吗？',
				success(res) {
					if (res.confirm) {
						that.canjia_bac();
					}
				}
			})

		},
		canjia_bac() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.uid = e.uid;
			params.paper_id = that.id;
			params.at_id = that.info['brisk_team']['id'];
			var b = app.globalData.api_root + 'User/participation_activities';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {

						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.get_article_info();
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		get_reply_user_del(d) {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			uni.showModal({
				title: '提示',
				content: '确定删除吗？',
				success(res) {
					if (res.confirm) {
						var params = new Object();
						params.token = e.token;
						params.openid = e.openid;
						params.id = d.currentTarget.dataset.id;
						var b = app.globalData.api_root + 'Article/get_reply_user_del';
						http.POST(b, {
							params: params,
							success: (res) => {
								console.log(res);
								if (res.data.status == "success") {
									that.get_reply_and_list = [];
									that.get_reply_and_page = 1;
									uni.showToast({
										title: res.data.msg,
										icon: 'none',
										duration: 2000
									})
									that.get_reply_and_hui();
								} else {
									uni.showToast({
										title: res.data.msg,
										icon: 'none',
										duration: 2000
									})
								}
							},
							fail: () => {
								uni.showModal({
									title: '提示',
									content: '网络繁忙，请稍候重试！',
									showCancel: false,
									success: (res) => { }
								})
							},
						})
					}
				}
			})

		},
		/**
		 * 回复的回复
		 */
		get_reply_user(dd) {
			var user_id = dd.currentTarget.dataset.user_id;
			var id = dd.currentTarget.dataset.id;
			var user_name = dd.currentTarget.dataset.user_name;
			this.placeholder_reply = '回复 ' + user_name + ':';
			this.get_reply_and_user_id = user_id;
			this.get_reply_and_id = id;
			this.focus = true;
		},
		/**
		 * 评论回复下一页
		 */
		page_get_reply() {
			this.get_reply_and_page = this.get_reply_and_page + 1;
			this.get_reply_and_hui();
		},
		/**
		 * 打开回复评论
		 */
		reply_and(data) {
			var that = this;
			var checkLogin = app.globalData.checkPhoneLogin(1);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}

			this.get_reply_and_hui_id = data.currentTarget.dataset.hui_id;
			this.get_reply_and_list = [];
			this.get_reply_and_page = 1;
			var that = this;
			// 创建一个动画实例
			var animation = uni.createAnimation({
				// 动画持续时间
				duration: 150,
				// 定义动画效果，当前是匀速
				timingFunction: 'ease'
			})
			// 将该变量赋值给当前动画
			that.animation = animation
			// 先在y轴偏移，然后用step()完成一个动画
			animation.translateY(550).step()
			// 用setData改变当前动画
			that.animationData = animation.export();
			// 改变view里面的Wx：if
			that.reply_and_hui = true;
			that.select = false;
			that.get_reply_and_hui();
			// 设置setTimeout来改变y轴偏移量，实现有感觉的滑动
			setTimeout(() => {
				animation.translateY(0).step()
				that.animationData = animation.export()
			}, 100)

		},
		/**
		 * 获取评论回复文字
		 */
		update_reply_text(e) {
			this.get_reply_and_text = e.detail.value;
		},
		/**
		 * 发表评论回复
		 */
		add_reply_and_hui() {
			console.log(1);
			if (this.get_reply_and_text == '') {
				uni.showToast({
					title: '内容不能为空',
					icon: 'none',
					duration: 2000
				})
				return;
			}
			if (app.globalData.__CheckTheCertification(this.user_info)) {
				this.refresh_user_info = true;
				return;
			}
			uni.showLoading({
				title: '正在回复...',
				mask: true,
			});
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.uid = e.uid;
			params.id = this.get_reply_and_hui_id;
			params.user_id = this.get_reply_and_user_id;
			params.and_id = this.get_reply_and_id;
			params.duplex_content = this.get_reply_and_text;
			if (this.top_info == '') {
				params.name_card = 0;
			} else {
				params.name_card = this.top_info.id;
			}
			var b = app.globalData.api_root + 'User/add_paper_reply_duplex';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						var subscribe = app.globalData.getCache("subscribe");
						if (subscribe && subscribe['YL0004'] && subscribe['YL0006'] && subscribe[
							'YL0007']) {
							app.globalData.authorization(subscribe['YL0004'], subscribe['YL0006'],
								subscribe['YL0007'], (res) => {

								})
						}
						that.get_reply_and_text = '';
						that.get_reply_and_list = [];
						that.placeholder_reply = '写评论';
						that.get_reply_and_page = 1;
						that.get_reply_and_user_id = 0;
						that.get_reply_and_hui();
					} else if (res.data.status == "error") {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							showCancel: false,
						})
						that.PhShow = false;
						that.get_reply_and_text = '';
						that.placeholder_reply = '写评论';
						that.get_reply_and_page = 1;
						that.get_reply_and_user_id = 0;
						that.duplex_content = ''
					} else {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							showCancel: false,
						})
						that.top_info = ''
					}
					uni.hideLoading();
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 获取评论详情
		 */
		get_reply_and_hui() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = that.get_reply_and_hui_id;
			params.page = this.get_reply_and_page;
			var b = app.globalData.api_root + 'User/get_paper_reply_info';
			var allMsg = that.get_reply_and_list;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.list.length == 0 || res.data.list.length < 4) {
						that.di_get_reply = true
					}
					for (var i = 0; i < res.data.list.length; i++) {
						allMsg.push(res.data.list[i]);
					}
					that.get_reply_and_info = res.data.info;
					that.get_reply_and_list = allMsg;
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 点开送礼
		 */
		add_liwu() {
			var checkLogin = app.globalData.checkPhoneLogin(0);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}
			this.isHuifuModalVisible = true;
			this.select = false;
			this.current = 'tab3'
			this.get_liwu_all();
		},
		add_zhuanfa(e) {

			if (this.scene == '1154') {
				return;
			}
			var e = app.globalData.getCache("userinfo");
			console.log(e);
			var that = this;
			//如果是游客
			var checkLogin = app.globalData.checkPhoneLogin(0);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}
			uni.showLoading({
				title: '加载中...',
				mask: true,
			});
			this.select = false;
			this.zhuanfa = true;
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;

			var srcReg = /<img [^>]*src=['"]([^'"]+)[^>]*>/;
			var tem = this.info['study_content'].match(srcReg);
			console.log(tem);
			if (tem) {
				params.img = tem[1];
			}
			this.check_img = tem;
			console.log(tem);
			var b = app.globalData.api_root + 'User/base64EncodeImage';
			http.POST(b, {
				params: params,
				success: (red) => {
					console.log(red);
					if (red.data.base != '') {
						//获取内容图片
						var showImgData = red.data.base.split(",");
						var buffer = util._base64ToArrayBuffer(showImgData[1]);
					}

					var fileName = `${uni.env.USER_DATA_PATH}` + '/share_img' + that.id + '.png'
					var showImgData1 = red.data.authority.split(",");
					var buffer1 = util._base64ToArrayBuffer(showImgData1[1]);
					var fileName1 = `${uni.env.USER_DATA_PATH}` + '/authority_img' + that.id + '.png'
					fsm.writeFileSync(fileName1, buffer1, 'binary');
					if (params.img) {
						fsm.writeFileSync(fileName, buffer, 'binary');
					}
					that.authority_title = red.data.title;
					that.fileName = fileName;
					that.authority_fileName = fileName1;
					uni.getImageInfo({
						src: fileName,
						success(res) {
							that.imgheght = res.height;
							that.imageWidth = res.width
						},
					})
					uni.hideLoading();
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})

		},
		no_zhuanfa() {
			this.select = true;
			this.zhuanfa = false;
		},
		set_img_quan_tap() {
			this.select = false;
			this.set_img_quan = true;
			this.zhuanfa = false;
			uni.pageScrollTo({
				scrollTop: 0,
			})
			this.getAvaterInfo();
		},
		/**
		 * 先下载头像图片
		 */
		getAvaterInfo() {
			uni.showLoading({
				title: '生成中...',
				mask: true,
			});
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.img = that.info.user_head_sculpture;
			var b = app.globalData.api_root + 'User/base64EncodeImage';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					that.getQrCode(res.data.base); //继续下载二维码图片
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		no_set_img_quan() {
			this.select = true;
			this.set_img_quan = false;
			this.zhuanfa = false;
		},
		/**
		 * 下载二维码图片
		 */
		getQrCode(avaterSrc) {
			uni.showLoading({
				title: '生成中...',
				mask: true,
			});
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = that.id;
			params.much_id = app.globalData.siteInfo.uniacid;
			var b = app.globalData.api_root + 'User/qrcode';
			uni.request({
				url: b,
				method: 'POST',
				data: params,
				responseType: 'arraybuffer',
				header: {
					'content-type': 'application/json,charset=utf-8'
				},
				success(red) {
					console.log(red);
					var base64 = uni.arrayBufferToBase64(red.data);
					that.sharePosteCanvas(avaterSrc, base64);
				}
			})
		},


		/**
		 * 开始用canvas绘制分享海报
		 * @param avaterSrc 下载的头像图片路径
		 * @param codeSrc   下载的二维码图片路径
		 */
		sharePosteCanvas(avaterSrc, codeSrc) {
			uni.showLoading({
				title: '生成中...',
				mask: true,
			})
			var that = this;
			var app_e = app.globalData.getCache("userinfo");
			var cardInfo = that.user_info; //需要绘制的数据集合
			console.log(cardInfo);
			const ctx = uni.createCanvasContext('myCanvas', that);
			var width = "";
			uni.createSelectorQuery().select('#canvas-container').boundingClientRect((rect) => {
				console.log(rect);
				var height = rect.height;
				var right = rect.right;
				var top = rect.top;
				width = rect.width;
				var zhen_width = rect.width - 20;
				var left = rect.left;
				ctx.drawImage('/static/yl_welore/style/icon/z.png', 0, 0, 300, 310);
				ctx.save()


				// //头像为正方形
				if (avaterSrc) {
					ctx.beginPath()
					ctx.fill()
					var avatarurl_width = 30; //绘制的头像宽度
					var avatarurl_heigth = 30; //绘制的头像高度
					var avatarurl_x = 20; //绘制的头像在画布上的位置
					var avatarurl_y = 165; //绘制的头像在画布上的位置
					//先画个圆   前两个参数确定了圆心 （x,y） 坐标  第三个参数是圆的半径  四参数是绘图方向  默认是false，即顺时针
					var headData = avaterSrc.split(",");
					const head_buffer = util._base64ToArrayBuffer(headData[1]);
					const fileName_head = `${uni.env.USER_DATA_PATH}` + '/head_img' + that.id + '.png'
					fsm.writeFileSync(fileName_head, head_buffer, 'binary');
					ctx.arc(avatarurl_width / 2 + avatarurl_x, avatarurl_heigth / 2 + avatarurl_y,
						avatarurl_width / 2, 0, Math.PI * 2, false);
					ctx.clip();
					ctx.drawImage(fileName_head, avatarurl_x, avatarurl_y, avatarurl_width, avatarurl_heigth);
				}

				//昵称
				ctx.restore();
				ctx.setFontSize(12);
				ctx.setFillStyle('#000000');
				ctx.setTextAlign('left');
				ctx.fillText(that.info.user_nick_name, avatarurl_x + 35, avatarurl_y + 20);
				if (that.info.chun_text != '') {
					ctx.font = 'normal 300 14px SimHei';
					ctx.setTextAlign('left')
					ctx.setFillStyle('#000');
					var fillText = that.substr(that.info.chun_text.replace(/<[^>]*>/g, '').replace(/\n/g, ''),
						15);
					ctx.fillText(fillText, 20, 70);
				}

				if (that.info.study_title != '') {
					ctx.font = 'normal 300 16px SimHei';
					ctx.setTextAlign('left')
					ctx.setFillStyle('#000');
					var study_title = that.substr(that.info.study_title.replace(/<[^>]*>/g, '').replace(/\n/g,
						''), 15);
					ctx.fillText(study_title, 20, 45, zhen_width);
				}
				ctx.font = 'normal 300 12px SimHei';
				ctx.setTextAlign('left')
				ctx.setFillStyle('#3B87F9');
				var fillText = that.substr(that.info.realm_name.replace(/<[^>]*>/g, '').replace(/\n/g, ''), 5);
				ctx.fillText(fillText, width - 95, avatarurl_y + 20);

				ctx.font = 'normal 600 12px SimHei';
				ctx.setTextAlign('center')
				ctx.setFillStyle('#000000');
				ctx.fillText(that.authority_title, 55, height - 25);
				ctx.save()
				ctx.beginPath()
				//  绘制二维码
				const buffer_code = util._base64ToArrayBuffer(codeSrc);
				console.log('buffer_code', buffer_code);
				const fileName_code = `${uni.env.USER_DATA_PATH}` + '/code_img' + that.id + '.png'
				fsm.writeFileSync(fileName_code, buffer_code, 'binary');
				ctx.drawImage(fileName_code, width - 83, avatarurl_y + 63, 70, 70)
				if (that.check_img) {
					ctx.drawImage(that.fileName, 20, 80, 65, 65);
					ctx.restore();
				}
				ctx.drawImage(that.authority_fileName, 32, height - 80, 45, 45);
				ctx.restore();

			}).exec()
			setTimeout(() => {
				ctx.draw();
				uni.canvasToTempFilePath({
					canvasId: 'myCanvas',
					success: (res) => {
						var tempFilePath = res.tempFilePath;
						that.imagePath_c = tempFilePath;
					},
					fail: (res) => {
						console.log(res);
					}
				});
				uni.hideLoading();

			}, 1000)
		},
		/**
		 * 绘制圆角矩形
		 * @param {*} x 起始点x坐标
		 * @param {*} y 起始点y坐标
		 * @param {*} w 矩形宽
		 * @param {*} h 矩形高
		 * @param {*} r 圆角半径
		 * @param {*} ctx 画板上下文
		 */
		darwRoundRect(x, y, w, h, r, ctx) {
			ctx.save()
			ctx.beginPath()

			// 左上弧线
			ctx.arc(x + r, y + r, r, 1 * Math.PI, 1.5 * Math.PI)
			// 左直线
			ctx.moveTo(x, y + r)
			ctx.lineTo(x, y + h - r)
			// 左下弧线
			ctx.arc(x + r, y + h - r, r, 0.5 * Math.PI, 1 * Math.PI)
			// 下直线
			ctx.lineTo(x + r, y + h)
			ctx.lineTo(x + w - r, y + h)
			// 右下弧线
			ctx.arc(x + w - r, y + h - r, r, 0 * Math.PI, 0.5 * Math.PI)
			// 右直线
			ctx.lineTo(x + w, y + h - r)
			ctx.lineTo(x + w, y + r)
			// 右上弧线
			ctx.arc(x + w - r, y + r, r, 1.5 * Math.PI, 2 * Math.PI)
			// 上直线
			ctx.lineTo(x + w - r, y)
			ctx.lineTo(x + r, y)
			ctx.closePath()
			//ctx.setFillStyle('white')
			ctx.clip();
		},

		//点击保存到相册
		saveShareImg() {
			var that = this;
			uni.showLoading({
				title: '正在保存',
				mask: true,
			})
			setTimeout(() => {
				uni.canvasToTempFilePath({
					canvasId: 'myCanvas',
					success: (res) => {
						uni.hideLoading();
						var tempFilePath = res.tempFilePath;
						uni.saveImageToPhotosAlbum({
							filePath: tempFilePath,
							success(res) {
								uni.showModal({
									content: '图片已保存到相册，赶紧晒一下吧~',
									showCancel: false,
									confirmText: '好的',
									confirmColor: '#333',
									success: (res) => {
										if (res.confirm) { }
									},
									fail: (res) => { }
								})
							},
							fail: (res) => {
								console.log(res);
								if (res.errMsg ==
									"saveImageToPhotosAlbum:fail auth deny") {
									uni.showModal({
										content: '检测到您未打开微信保存图片到相册，开启后即可保存图片',
										confirmText: '去开启',
										success(res) {
											if (res.confirm) {
												uni.openSetting({
													success(res) { }
												})
											} else if (res.cancel) {

											}
										}
									})
								}
							}
						})
					},
					fail: () => {
						console.log(err)
					}
				}, that);
			}, 1000);
		},
		substr(val, num) {
			if (val.length == 0 || val == undefined) {
				return false;
			} else if (val.length > num) {
				return val.substring(0, num) + "...";
			} else {
				return val;
			}
		},
		/**
		 * 获取排行
		 */
		get_liwu_ph() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.uid = this.info['user_id'];
			params.limit = 3;
			var b = app.globalData.api_root + 'User/get_user_guard';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						that.liwu_ph = res.data.info;
						that.liwu_count = res.data.count
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		handleCancel1() {
			this.visible = false;
			this.select = true;
			this.visible1 = false;
		},
		handleCancel2() {

			if (this.scene == '1154') {
				return;
			}
			var checkLogin = app.globalData.checkPhoneLogin(0);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}


			this.actions = [];
			this.actions2 = [];
			var info = this.info;
			console.log(info);
			var actions = this.actions;
			if (info['user_id_time'] == 1 || info['user_id'] == this.uid || info['is_qq'] == 'da' || info['is_qq'] ==
				'xiao' || this.admin == 1) {
				actions.push({
					name: '删除',
					type: 'sc',
					is_qq: info['is_qq'],
					icon: 'trash',
				});
			}
			if (info['is_qq'] == 'da' || info['is_qq'] == 'xiao' || this.admin == 1) {

				if (info['user_id'] != this.uid) {
					actions.push({
						name: '禁言',
						type: 'jy',
					});
				}
				if (info['topping_time'] != 0) {
					actions.push({
						name: '取消置顶',
						type: 'qxzd',
					});
				} else {
					actions.push({
						name: '置顶',
						type: 'zd',
					});
				}
				if (info['essence_time'] != 0) {
					actions.push({
						name: '取消推荐',
						type: 'qxtj',
					});
				} else {
					actions.push({
						name: '推荐',
						type: 'tj',
					});
				}

			}
			if (this.admin == 1 && this.info_type == 3 && this.info.brisk_team.is_approve == 0) {
				actions.push({
					name: '认证活动',
					type: 'rzhd',
				});
			}
			if (this.admin == 1 && this.info_type == 3 && this.info.brisk_team.is_approve == 1) {
				actions.push({
					name: '取消认证',
					type: 'rzhd',
				});
			}
			if (info['user_id'] != this.uid && info['is_qq'] == 'no' && this.admin == 0) {
				actions.push({
					name: '举报',
					type: 'jb',
				});
			}
			if (info['user_id'] == this.uid) {
				actions.push({
					name: info.img_show_type == 0 ? '转九宫格样式' : '转图文样式',
					type: 'jgg',
				});
			}
			console.log(actions);
			this.huoqu_tie = true;
			this.visible = true;
			this.select = false;
			this.jinyan = false;
			this.actions = actions;
		},
		handleCancel3(e) {
			console.log(e);
			this.actions1 = [];
			this.del_type = e.currentTarget.dataset.type
			var user_id = e.currentTarget.dataset.user_id;
			var actions1 = this.actions1;
			var info = this.info;
			if (info['user_id'] == this.uid || info['is_qq'] == 'da' || info['is_qq'] == 'xiao' || this.admin == 1) {
				actions1.push({
					name: '删除',
					type: 'sc',
					id: e.currentTarget.dataset.id,
					is_qq: info['is_qq'],
					user_id: user_id,
					key: e.currentTarget.dataset.key
				});
			}
			if (user_id == this.uid && info['is_qq'] == 'no' && info['user_id'] != this.uid) {
				actions1.push({
					name: '删除',
					type: 'sc',
					id: e.currentTarget.dataset.id,
					is_qq: info['is_qq'],
					user_id: user_id,
					key: e.currentTarget.dataset.key
				});
			}
			if (user_id != this.uid && info['is_qq'] == 'no' && this.admin == 0) {
				actions1.push({
					name: '举报',
					type: 'jb',
					id: e.currentTarget.dataset.id,
					user_id: user_id,
				});
			}
			if (user_id != this.uid) {
				if (info['is_qq'] == 'da' || info['is_qq'] == 'xiao' || this.admin == 1) {
					actions1.push({
						name: '禁言',
						type: 'jy',
						user_id: user_id,
					});
					var actions2 = this.actions2;
					actions2.push({
						name: '1天',
						day: 1,
						user_id: user_id
					}, {
						name: '3天',
						day: 3,
						user_id: user_id
					}, {
						name: '7天',
						day: 7,
						user_id: user_id
					}, {
						name: '15天',
						day: 15,
						user_id: user_id
					}, {
						name: '30天',
						day: 30,
						user_id: user_id
					});

					this.actions2 = actions2;
				}

			}
			this.huoqu_ping = true;
			this.visible1 = true;
			this.select = false;
			this.jinyan = false;
			this.actions1 = actions1;
		},
		/**
		 * 回复操作
		 */
		huifu_select(detail) {
			var that = this;
			var index = detail.currentTarget.dataset.index;
			var actions1 = this.actions1;
			var select = actions1[index];
			console.log(select);
			if (select['type'] == 'jy') {
				this.actions2 = [];
				var actions2 = this.actions2;
				actions2.push({
					name: '1天',
					day: 1,
					user_id: select['user_id']
				}, {
					name: '3天',
					day: 3,
					user_id: select['user_id']
				}, {
					name: '7天',
					day: 7,
					user_id: select['user_id']
				}, {
					name: '15天',
					day: 15,
					user_id: select['user_id']
				}, {
					name: '30天',
					day: 30,
					user_id: select['user_id']
				}, {
					name: '1年',
					day: 365,
					user_id: select['user_id']
				});
				this.visible1 = false;
				this.jinyan = true;
				this.actions2 = actions2;
			}
			if (select['type'] == 'jb') {
				this.visible1 = false;
				this.kkk = 1;
				this.type_id = select['id'];
				this.jubao = true;
			}
			if (select['type'] == 'sc') {
				if (select['is_qq'] == 'da' || select['is_qq'] == 'xiao' || this.admin == 1) {
					if (select['user_id'] != this.uid) {
						this.visible1 = false;
						this.del_msg = true;
						this.huifu_id = select['id'];
					} else {
						this.huoqu_ping = false;
						this.huifu_id = select['id'];
						this.huifu_key = select['key'];
						uni.showModal({
							title: '提示',
							content: '确定要删除这个回复吗？',
							success(res) {
								if (res.confirm) {
									that.del_do();
								} else if (res.cancel) {
									console.log('用户点击取消')
								}
							}
						})
					}
				} else {
					this.huoqu_ping = false;
					this.id = select['id'];
					this.huifu_id = select['id'];
					this.key = select['key'];
					uni.showModal({
						title: '提示',
						content: '确定要删除这个回复吗？',
						success(res) {
							if (res.confirm) {
								that.del_do();
							} else if (res.cancel) {
								console.log('用户点击取消')
							}
						}
					})
				}
			}
		},
		select_jy(dd) {
			var index = dd.currentTarget.dataset.index;
			var select = this.actions2[index];
			this.jinyan_index = index;
			this.actions_index = select;
		},
		/**
		 * 帖子操作
		 */
		tiezi_select(detail) {
			var that = this;
			var index = detail.currentTarget.dataset.index;
			var actions = this.actions;
			var select = actions[index];
			var info = this.info;
			console.log(select);
			var actions2 = this.actions2;
			if (select['type'] == 'jy') {
				actions2.push({
					name: '1天',
					day: 1,
					user_id: info['user_id']
				}, {
					name: '3天',
					day: 3,
					user_id: info['user_id']
				}, {
					name: '7天',
					day: 7,
					user_id: info['user_id']
				}, {
					name: '15天',
					day: 15,
					user_id: info['user_id']
				}, {
					name: '30天',
					day: 30,
					user_id: info['user_id']
				}, {
					name: '1年',
					day: 365,
					user_id: info['user_id']
				});
				this.visible = false;
				this.jinyan = true;
				this.actions2 = actions2;
			}
			if (select['type'] == 'jb') {
				this.visible = false;
				this.jubao = true;
				this.kkk = 0;
				this.type_id = 0;
			}
			if (select['type'] == 'zd') {
				this.visible = false;
				this.select = true;
				this.huoqu_tie = false;
				this.placement();
			}
			if (select['type'] == 'qxzd') {
				this.visible = false;
				this.select = true;
				this.huoqu_tie = false;
				this.placement();
			}
			if (select['type'] == 'sc') {
				if (select['is_qq'] == 'da' || select['is_qq'] == 'xiao' || this.admin == 1) {
					if (info['user_id'] != this.uid) {
						this.visible = false;
						this.tz_del_msg = true;
					}
				}
				if (info['user_id'] == this.uid || info['user_id_time'] == 1) {
					this.huoqu_tie = false;
					uni.showModal({
						title: '提示',
						content: '确定要删除这个帖子吗？',
						success(res) {
							if (res.confirm) {
								that.del_tz_do();
							} else if (res.cancel) {
								console.log('用户点击取消')
							}
						}
					})


				}
				this.visible = false;
			}
			if (select['type'] == 'tj') {
				this.set_essence(info['id']);
				this.visible = false;
				this.huoqu_tie = false;
			}
			if (select['type'] == 'qxtj') {
				this.del_essence(info['id']);
				this.visible = false;
				this.huoqu_tie = false;
			}
			if (select['type'] == 'rzhd') {
				this.set_brisk_team(info['id']);
			}
			if (select['type'] == 'jgg') {
				this.set_img_show_type();
			}
		},
		set_img_show_type() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.id;
			var b = app.globalData.api_root + 'Article/set_img_show_type';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.get_article_info();
						that.hideModal();
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		set_brisk_team(id) {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.paper_id = id;
			params.is_approve = this.info.brisk_team.is_approve;
			var b = app.globalData.api_root + 'Article/set_brisk_team';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.get_article_info();
						that.hideModal();
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 推荐帖子
		 */
		set_essence(id) {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = id;
			var b = app.globalData.api_root + 'User/set_essence';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.get_article_info();
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 删除推荐帖子
		 */
		del_essence(id) {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = id;
			var b = app.globalData.api_root + 'User/del_essence';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.get_article_info();
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 提交禁言操作
		 */
		do_banned_user() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();

			params.token = e.token;
			params.openid = e.openid;
			params.uid = e.uid;
			params.user_id = this.actions_index['user_id'];
			params.day = this.actions_index['day'];
			params.refer_type = this.info['is_qq'];
			params.tory_id = this.info['tory_id'];
			params.beget = this.reasonText;
			if (this.reasonText == '') {
				uni.showToast({
					title: '请填写理由！',
					icon: 'none',
					duration: 2000
				})
				return;
			}
			var b = app.globalData.api_root + 'User/do_banned_user';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						that.hideModal();
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					} else {
						that.hideModal();
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}

				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 帖子置顶
		 */
		placement() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.id;
			var b = app.globalData.api_root + 'User/placement';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.get_article_info();
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 图片和语音切换
		 */
		handleChange(detail) {
			var key = detail.currentTarget.dataset.key;
			console.log(detail);
			var that = this;
			this.current = key;
			if (key == 'tab2') {
				uni.authorize({
					scope: 'scope.record',
					fail() {
						that.visible2 = true;
						that.scope_record = false;
					}
				})
			}
		},
		handleOpen5() {
			this.del_mod = true;
		},
		/**
		 * 回复
		 */
		handleChange_h(detail) {
			if (this.scene == '1154') {
				return;
			}
			var key = detail.currentTarget.dataset.key;
			this.current_h = key;
			if (key == 'h1') {
				show_type = 'all';
			}
			if (key == 'h2') {
				show_type = 'main';
			}
			if (key == 'h3') {
				show_type = 'my';
			}
			this.hui_fu_page(1, show_type);
		},

		get_one_name_card(name_card) {
			var b = app.globalData.api_root + 'Nameplate/get_one_name_card';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.name_id = name_card;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						this.top_info = res.data.info;
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		doIt() {
			app.globalData.getLogin(
				// 成功回调 returnA 
				(userInfo) => {
					console.log(' 登录成功123:', userInfo);
					this.authority();
					this.get_article_info();
					this.get_ad();
					this.get_user_info();
					this.get_subscribe();
					this.get_diy();
				},
				// 失败回调 returnB 
				(err) => {
					console.error(' 登录失败:', err);
				}
			);

		},
		//获取订阅信息
		get_subscribe() {
			var subscribe = app.globalData.getCache("subscribe");
			if (!subscribe) {
				app.globalData.subscribe_message((res) => {
					//请求成功的回调函数
					if (res == '') {
						return;
					}
					app.globalData.setCache("subscribe", res.parallelism_data);
				}, () => {
					//请求失败的回调函数，不需要时可省略
				})
			} else {
				return 1;
			}
		},
		//do
		async do_paper_money() {
			this.purchase_paper_mod = false;
			var b = app.globalData.api_root + 'User/do_paper_money_new';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.money_id = this.id;
			try {
				const res = await http.POST(b, params);
				console.log(res);
				if (res.data.status == 'success') {
					if (res.data.code == 1) {
						uni.showModal({
							title: '提示',
							confirmText: '去网盘',
							content: res.data.msg,
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/yl_welore/pages/packageF/netdisc/index',
									})
								}
							}
						})
					} else {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							showCancel: false,
							success: (res) => { }
						})
					}
					this.purchase_paper_mod = false;
					if (that.info.study_type == 3) {
						that.canjia_bac();
					}

					that.get_article_info();
				} else {
					if (res.data.error_code == -1) {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							cancelText: '我的钱包',
							success: (res) => {
								if (res.confirm) { } else if (res.cancel) {
									uni.navigateTo({
										url: '/yl_welore/pages/packageC/user_details/index',
									})
								}
							}
						})
					}
					uni.showModal({
						title: '提示',
						content: res.data.msg,
						showCancel: false,
						success: (res) => { }
					})
				}
			} catch (error) {
				uni.showModal({
					title: '提示',
					content: '网络繁忙，请稍候重试！',
					showCancel: false,
					success: (res) => { }
				})
			}
		},
		//获取用户信息
		get_user_info() {
			var e = app.globalData.getCache("userinfo");
			var b = app.globalData.api_root + 'User/get_article_user_info';
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data['status'] == 'feng') {
						uni.navigateTo({
							url: '/yl_welore/pages/black_house/index?msg=' + res.data.user
								.forbid_prompt + '&open_id=' + res.data.user.open_id,
						})
					}
					if (res.data.status == 'success') {
						this.user_info = res.data.info;
						if (res.data.info.status == 0) {
							uni.navigateTo({
								url: '/yl_welore/pages/black_house/index',
							})
							return;
						}
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
			// return new Promise(async (resolve, reject) => {
			//     var b = app.globalData.api_root + 'User/get_article_user_info';
			//     var that = this;
			//     var e = app.globalData.getCache("userinfo");
			//     //console.log(e);
			//     var params = new Object();
			//     params.token = e.token;
			//     params.openid = e.openid;
			//     try {
			//         const res = await http.POST(b, params);
			//         console.log(res);
			//         if (res.data['status'] == 'feng') {
			//             uni.navigateTo({
			//                 url: '/yl_welore/pages/black_house/index?msg=' + res.data.user.forbid_prompt + '&open_id=' + res.data.user.open_id,
			//             })
			//         }
			//         if (res.data.status == 'success') {
			//             this.user_info = res.data.info;
			//             if (res.data.info.status == 0) {
			//                 uni.navigateTo({
			//                     url: '/yl_welore/pages/black_house/index',
			//                 })
			//                 return;
			//             }
			//         } else {
			//             uni.showToast({
			//                 title: res.data.msg,
			//                 icon: 'none',
			//                 duration: 2000
			//             })
			//         }
			//     } catch (error) {
			//         reject(error);
			//     }
			// });
		},
		get_diy() {
			var b = app.globalData.api_root + 'User/get_diy';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.uid = e.uid;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status) {
						return;
					} else {
						app.globalData.store.setState({
							diy: res.data
						});
						var n = parseInt(+new Date / 1000) + 129600;
						uni.setStorageSync("is_diy", res.data);
						app.globalData.setCache('over_time', n);
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 送礼数量
		 */
		handleChange1({
			detail
		}) {
			var num = detail.value;
			var li_info = this.li_list[this.li_index];
			this.li_number = num;
			this.li_sum = (li_info['tr_conch'] * num).toFixed(2);
		},
		/**
		 * 打赏
		 */
		reward() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.num = this.li_number;
			params.uid = e.uid;
			params.paper_id = this.id;
			params.user_id = this.info['user_id'];
			params.li_id = this.li_list[this.li_index]['id'];
			var b = app.globalData.api_root + 'User/user_reward_new';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.get_user_info();
						that.hui_fu_page(1);
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 点击送礼
		 */
		liwu_index(e) {
			var li_index = e.currentTarget.dataset.k;
			var id = e.currentTarget.dataset.id;

			var li_info = this.li_list[li_index];
			this.li_index = li_index;
			this.id = id;
			this.li_number = 1;
			this.li_sum = li_info['tr_conch'];
		},
		/**
		 * 获取礼物列表
		 */
		get_liwu_all() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.uid = e.uid;
			var b = app.globalData.api_root + 'User/get_liwu';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						this.li_list = res.data.info;
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 流量主
		 */
		get_ad() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			var b = app.globalData.api_root + 'User/get_ad';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {

						that.ad = res.data.info;
						app.globalData.store.setState({
							ad: res.data.data
						});
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 回复赞
		 */
		get_huifu_zan(dd) {
			var hui_id = dd.currentTarget.dataset.kkk;
			var index = dd.currentTarget.dataset.index;
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.id;
			params.uid = e.uid;
			params.hui_id = hui_id;
			var b = app.globalData.api_root + 'User/add_paper_prely';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.vibrateShort();
						var huifu_list = that.huifu_list;

						that.huifu_list[index].is_huifu_zan = huifu_list[index]['is_huifu_zan'] == false ?
							true : false;
						that.huifu_list[index].is_huifu_zan_count = huifu_list[index]['is_huifu_zan'] ==
							false ? parseInt(huifu_list[index]['is_huifu_zan_count']) + 1 : parseInt(
								huifu_list[index]['is_huifu_zan_count']) - 1;

					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 关闭举报
		 */
		no_jubao() {
			this.jubao = false;
			this.select = true;
			this.visible = true;
		},
		/**
		 * 记录举报内容
		 */
		get_jubao_text(e) {
			var text = e.detail.value;
			this.jubao_text = text;
		},

		/**
		 * 举报提交
		 */
		jubao_submit(e) {
			var kkk = this.kkk;
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.type_id == 0 ? this.id : this.type_id;
			params.uid = e.uid;
			params.tale_type = kkk;
			params.content = that.jubao_text;
			var b = app.globalData.api_root + 'User/add_paper_complaint';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
						})
						that.hideModal();
					} else {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 点赞
		 */
		add_zan(dd) {
			console.log(app.globalData.checkPhoneLogin());
			if (this.scene == '1154') {
				return;
			}
			var kkk = dd.currentTarget.dataset.kkk;
			var that = this;


			var checkLogin = app.globalData.checkPhoneLogin(1);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}

			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.id;
			params.uid = e.uid;
			params.zan_type = this.info['is_info_zan'] == true ? 1 : 0;
			params.applaud_type = kkk;
			var b = app.globalData.api_root + 'User/add_user_zan';


			uni.vibrateShort();

			var info = that.info;

			uni.vibrateShort();
			console.log(info['is_info_zan']);
			if (info['is_info_zan'] == false) {
				that.info.is_info_zan = true;
				that.info.info_zan_count_this = parseInt(info['info_zan_count_this']) + 1;


			} else {
				that.info.is_info_zan = false;
				that.info.info_zan_count_this = parseInt(info['info_zan_count_this']) - 1;
			}
			console.log(that.info);

			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {


					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 收藏
		 */
		add_sc() {
			if (this.scene == '1154') {
				return;
			}
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var checkLogin = app.globalData.checkPhoneLogin(0);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.id;
			params.uid = e.uid;
			params.sc_type = this.info['is_info_sc'] == true ? 1 : 0;

			uni.vibrateShort();
			var info = that.info;
			console.log(info['is_info_sc']);
			if (info['is_info_sc'] == false) {

				that.info.is_info_sc = true;
				that.info.info_sc_count_this = parseInt(info['info_sc_count_this']) + 1;


			} else {

				that.info.is_info_sc = false;
				that.info.info_sc_count_this = parseInt(info['info_sc_count_this']) - 1;


			}

			var b = app.globalData.api_root + 'User/add_user_collect';
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {

					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 发布回复
		 */
		submit() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			if (this.current == 'tab1') {
				if (this.text == '' && this.img_arr.length == 0) {

					uni.showToast({
						title: '回复内容不能为空',
						icon: 'none',
						duration: 2000
					})
					return;
				}
			}
			if (this.current == 'tab2') {
				if (this.file == '') {
					uni.showToast({
						title: '语音内容不能为空',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				if (this.file_ss < 3) {
					uni.showToast({
						title: '语音内容太短了',
						icon: 'none',
						duration: 2000
					})
					return;
				}
			}
			uni.showLoading({
				title: '回复中...',
				mask: true,
			});
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.id;
			params.uid = e.uid;
			params.reply_type = this.current == 'tab1' ? 0 : 1;
			params.text = this.text;
			params.file_ss = this.file_ss;
			params.img_arr = this.img_arr;
			if (this.top_info == '') {
				params.name_card = 0;
			} else {
				params.name_card = this.top_info['id'];
			}
			if (this.file != '') {
				uni.uploadFile({
					url: app.globalData.api_root + 'User/img_upload',
					filePath: that.file,
					name: 'sngpic',
					header: {
						"content-type": "multipart/form-data"
					},
					formData: {
						"content-type": "multipart/form-data",
						'token': e.token,
						'openid': e.openid,
						'much_id': app.globalData.siteInfo.uniacid
					},
					success: (res) => {
						console.log(res);
						var data = JSON.parse(res.data);
						//var data = JSON.parse(decodeURIComponent(atoba.weAtob(res.data)));
						console.log(data);
						params.file = data.url;
						that.post(params);
					},
					fail: (res) => {
						uni.showToast({
							title: '语音内容不能为空',
							icon: 'error',
							duration: 2000
						})
					}
				});
			} else {
				params.file = this.file;
				that.post(params);
			}
		},
		post(params) {
			console.log(params);
			var b = app.globalData.api_root + 'User/add_paper_reply_new';
			var that = this;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						uni.showModal({
							title: '提示',
							content: res.data.msg,
							success(res) {
								var subscribe = app.globalData.getCache("subscribe");
								if (subscribe && subscribe['YL0004'] && subscribe['YL0006'] &&
									subscribe['YL0007']) {
									app.globalData.authorization(subscribe['YL0004'], subscribe[
										'YL0006'], subscribe['YL0007'], (res) => {

										})
								}
							}
						})
						that.isHuifuModalVisible = false;
						that.file = '';
						that.img_arr = [];
						that.di_msg = false;
						that.select = true;
						that.img_botton = true;
						that.file_ss = 0;
						that.get_article_info(1);
						uni.hideLoading();
					} else if (res.data.status == "error") {
						uni.hideLoading();
						uni.showModal({
							title: '提示',
							content: res.data.msg,
						})
					} else {
						uni.hideLoading();
						uni.showModal({
							title: '提示',
							content: res.data.msg,
						})
						that.top_info = '';
					}
					that.text = '';
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 打开地图
		 */
		get_position(d) {
			var a = Number(d.currentTarget.dataset.latitude);
			var o = Number(d.currentTarget.dataset.longitude);
			var name = d.currentTarget.dataset.pos_name;
			if (a && o) {
				uni.openLocation({
					latitude: a,
					longitude: o,
					name: name
				})
			}
		},
		//播放声音
		ting_play(e) {
			var nuw = this.huifu_list;
			for (var i = 0; i < nuw.length; i++) {
				nuw[i]['is_voice'] = false;
			}
			this.huifu_list = nuw;
			var index = e.currentTarget.dataset.key;
			innerAudioContext.src = e.currentTarget.dataset.vo;
			innerAudioContext.title = '暂无标题';
			innerAudioContext.play();

			nuw[index]['is_voice'] = true;
			this.huifu_list = nuw;
			this.huifu_list_index = index;
			innerAudioContext.onEnded((res) => {
				nuw[index]['is_voice'] = false;
				this.huifu_list = nuw;
			})
		},
		/**
		 * 停止
		 */
		ting_stop(e) {
			innerAudioContext.stop();
			var index = e.currentTarget.dataset.key;
			var nuw = this.huifu_list;
			nuw[index]['is_voice'] = false;
			this.huifu_list = nuw;
		},
		imageUtil() {

			var video_auto_arbor = this.copyright['video_auto_arbor'];

			var imageSize = {};
			var originalWidth = this.info.study_video_bulk[0]; //图片原始宽
			var originalHeight = this.info.study_video_bulk[1]; //图片原始高
			if (originalWidth == 0 || video_auto_arbor == 0) {
				this.vd_width = '100%';
				this.vd_height = '198px';
				return;
			}
			var originalScale = originalHeight / originalWidth; //图片高宽比
			console.log('originalWidth: ' + originalWidth)
			console.log('originalHeight: ' + originalHeight)
			//获取屏幕宽高
			uni.getSystemInfo({
				success: (res) => {
					var windowWidth = res.windowWidth - 120;
					var windowHeight = res.windowHeight;
					var windowscale = windowHeight / windowWidth; //屏幕高宽比
					console.log('windowWidth: ' + windowWidth)
					console.log('windowHeight: ' + windowHeight)
					if (originalScale < windowscale) { //图片高宽比小于屏幕高宽比
						//图片缩放后的宽为屏幕宽
						imageSize.imageWidth = windowWidth;
						imageSize.imageHeight = (windowWidth * originalHeight) / originalWidth;
					} else { //图片高宽比大于屏幕高宽比
						//图片缩放后的高为屏幕高
						imageSize.imageHeight = windowHeight;
						imageSize.imageWidth = (windowHeight * originalWidth) / originalHeight;
					}
				}
			})
			console.log('缩放后的宽: ' + imageSize.imageWidth)
			console.log('缩放后的高: ' + imageSize.imageHeight)
			this.vd_width = imageSize.imageWidth + 'px';
			this.vd_height = imageSize.imageHeight + 'px';
			//return imageSize;
		},
		/**
		 * 获取帖子详情
		 */
		get_article_info(add_type = 0) {
			var b = app.globalData.api_root + 'User/get_article_info';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.id;
			params.uid = e.uid;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						that.info = res.data.info;
						that.version = res.data.info.version;
						that.admin = res.data.info.admin;
						app.globalData.setCache('article', res.data.info);
						that.imageUtil();
						if (res.data.info.is_open == 0) { //禁止转发
							uni.hideShareMenu();
						}
						if (that.scene == '1154') {
							uni.hideLoading();
							return;
						}
						that.hui_fu_page(add_type);
						if (that.copyright.guard_arbor == 1) {
							that.get_liwu_ph();
						}
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						setTimeout(() => {
							if (res.data.msg == "该信息已被删除") {
								that._navback();
							}
						}, 1500);
					}
					uni.hideLoading();
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 回复翻页
		 */
		hui_fu_page(add_type, show_type = 'all') {
			if (this.scene == 1154) {
				return;
			}
			var that = this;
			if (add_type == 1) {
				that.huifu_list = [];
				that.page = 1;
			}
			var b = app.globalData.api_root + 'User/get_article_huifu';

			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = this.id;
			params.uid = e.uid;
			params.page = this.page;
			params.show_type = show_type;
			var allMsg = that.huifu_list;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						if (res.data.huifu.length == 0 || res.data.huifu.length < 3) {
							that.di_msg = true;
						}
						for (var i = 0; i < res.data.huifu.length; i++) {
							allMsg.push(res.data.huifu[i]);
						}
						that.huifu_list = allMsg;


					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 回复上传主图
		 */
		previewOneImage() {
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var b = app.globalData.api_root + 'User/img_upload';
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
				success: (res) => {
					console.log(res);
					that.loadModal = true;
					var tempFilePaths = res.tempFilePaths;
					uni.uploadFile({
						url: b,
						filePath: tempFilePaths[0],
						name: 'sngpic',
						header: {
							"content-type": "multipart/form-data"
						},
						formData: {
							"content-type": "multipart/form-data",
							'token': e.token,
							'openid': e.openid,
							'much_id': app.globalData.siteInfo.uniacid
						},
						success: (res) => {
							console.log(res);
							var data = JSON.parse(res.data);
							console.log(data);
							if (data.status == 'error') {
								that.loadModal = false;
								uni.showModal({
									title: '提示',
									content: data.msg,
								})
								return;
							} else {
								that.img_arr = that.img_arr.concat(data.url);
								that.img_botton = false;

							}
							that.loadModal = false;
						},
						fail: (res) => {
							uni.showModal({
								title: '提示',
								content: '上传错误！',
							})
						}
					});

				}
			})
		},
		/**
		 * 删除回复
		 */
		del_huifu(e) {
			console.log(e);
			var id = e.id
			this.huifu_id = id;
			this.huifu_key = e.key;
			this.del_mod = true;
		},
		/**
		 * 删除图片
		 */
		clearOneImage(e) {
			var that = this;
			var index = e.target.dataset['index'];
			var notes = that.img_arr;
			notes.splice(index, 1);
			that.img_arr = notes;
			that.img_botton = true;
		},
		/**
		 * 内容
		 */
		get_text(e) {
			var length = e.detail.value;
			this.text = length;
		},
		/**
		 * 评论
		 */
		addhuifu(e) {
			var that = this;
			var checkLogin = app.globalData.checkPhoneLogin(1);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}

			if (app.globalData.__CheckTheCertification(this.user_info)) {
				this.refresh_user_info = true;
				return;
			}
			// 创建一个动画实例
			var animation = uni.createAnimation({
				// 动画持续时间
				duration: 150,
				// 定义动画效果，当前是匀速
				timingFunction: 'linear'
			})
			// 将该变量赋值给当前动画
			that.animation = animation
			// 先在y轴偏移，然后用step()完成一个动画
			animation.translateY(400).step()
			// 用setData改变当前动画
			that.animationData = animation.export();
			// 改变view里面的Wx：if
			that.isHuifuModalVisible = true;
			that.select = false;
			that.current = 'tab1';
			that.get_liwu_all();
			// 设置setTimeout来改变y轴偏移量，实现有感觉的滑动
			setTimeout(() => {
				animation.translateY(0).step()
				that.animationData = animation.export();
			}, 100)
		},
		/**
		 * 关闭回复
		 */
		no_huifu() {
			var that = this;
			var animation = uni.createAnimation({
				duration: 5,
				timingFunction: 'linear'
			})
			animation.translateY(0).step()
			that.animationData = animation.export();
			that.isHuifuModalVisible = false;
			that.select = true;
			that.reply_and_hui = false;
			that.PhShow = false;

		},
		PreviewViewImage(e) {
			uni.previewImage({
				urls: e.currentTarget.dataset.src,
				current: e.currentTarget.dataset.src
			});
		},
		ViewImage(e) {
			uni.previewImage({
				urls: this.info['forward_img'],
				current: e.currentTarget.dataset.url
			});
		},
		/**
		 * 图片预览
		 */
		previewImage(e) {
			this.show = false;
			var current = e.target.dataset.src;
			var urls = this.info['image_part'];
			uni.previewImage({
				current: current, // 当前显示图片的http链接  
				urls: urls // 需要预览的图片http链接列表  
			})
		},
		previewHuiImage(e) {
			this.show = false;
			var current = e.target.dataset.src;
			var index = e.target.dataset.index;
			uni.previewImage({
				current: current, // 当前显示图片的http链接  
				urls: this.huifu_list[index]['image_part'] // 需要预览的图片http链接列表  
			})
		},
		previewHuiAndImage(e) {
			this.show = false;
			var current = e.target.dataset.src;
			uni.previewImage({
				current: current, // 当前显示图片的http链接  
				urls: this.get_reply_and_info['image_part'] // 需要预览的图片http链接列表  
			})
		},
		/**
		 * 礼物列表
		 */
		url_is_one(d) {
			var id = d.currentTarget.dataset.id;
			uni.navigateTo({
				url: '/yl_welore/pages/packageB/user_guard/index?id=' + id
			})

		},
		/**
		 * 加载下一页
		 */
		onReachBottom() {
			this.page = this.page + 1;
			this.hui_fu_page();

		},


		//播放声音
		play(e) {
			var that = this;
			var index = e.currentTarget.dataset.key;
			var nuw = this.info;
			var key = 1;
			uni.getBackgroundAudioPlayerState({
				success(res) {
					console.log(res);
					const status = res.status
					key = res.status;
				}
			})
			nuw['is_voice'] = false;
			that.info = nuw;
			innerAudioContext.src = nuw['study_voice'];
			innerAudioContext.title = nuw['user_nick_name'] + '上传的音乐';

			innerAudioContext.onTimeUpdate(() => {
				//console.log(innerAudioContext.currentTime)
				var duration = innerAudioContext.duration;
				var offset = innerAudioContext.currentTime;
				var currentTime = parseInt(innerAudioContext.currentTime);
				var min = "0" + parseInt(currentTime / 60);
				var sec = currentTime % 60;
				if (sec < 10) {
					sec = "0" + sec;
				};
				var starttime = min + ':' + sec; /*  00:00  */

				nuw['starttime'] = starttime;
				nuw['offset'] = offset;
				that.info = nuw;
			})
			// innerAudioContext.play();

			nuw['is_voice'] = true;
			that.info = nuw;
			//播放结束
			innerAudioContext.onEnded(() => {
				var nuw = this.info;
				nuw['is_voice'] = false;
				that.starttime = '00:00';
				that.offset = 0;
				that.info = nuw;
				console.log("音乐播放结束");
			})
			innerAudioContext.play();
		},
		/**
		 * 停止
		 */
		stop(e) {
			innerAudioContext.pause();
			console.log('暂停');
			var index = e.currentTarget.dataset.key;
			var nuw = this.info;
			nuw['is_voice'] = false;
			this.info = nuw;
		},
		// 进度条拖拽
		sliderChange(e) {
			var index = e.currentTarget.dataset.key;
			var that = this
			var offset = parseInt(e.detail.value);
			innerAudioContext.play();
			innerAudioContext.seek(offset);
			var nuw = this.info;
			nuw['is_voice'] = true;
			this.info = nuw;
		},
		handleOk() {
			uni.openSetting();
			this.visible2 = false;
		},
		handleClose() {
			this.visible2 = false;
			this.current = 'tab1';
		},
		/**
		 * 按压
		 */
		touchStart() {
			var that = this;
			uni.getSetting({
				success(res) {
					if (res.authSetting['scope.record'] == false) {
						uni.authorize({
							scope: 'scope.record',
							fail() {
								that.visible2 = true;
								that.scope_record = false;
							}
						})
					} else {
						that.scope_record = true;
					}
				}
			})
			if (that.scope_record == false) {
				return false;
			}
			uni.showToast({
				title: '正在录音',
				duration: 9999999,
				image: '/yl_welore/style/icon/yuyin.png',
				mask: false
			});
			this.start();
		},
		touchEnd() {
			uni.hideToast();
			console.log('touchEnd');
			this.stop_hf()
		},
		//开始录音的时候
		start() {
			console.log('start');
			var that = this;
			that.file_ss = 0;
			that.star_recorder = true;
			//开始录音
			recorderManager_hf.start(options_hf);
			recorderManager_hf.onStart(() => {
				recordTimeInterval_a = setInterval(() => {
					var ss = that.file_ss + 1;
					if (ss >= 300) {
						that.touchEnd();
					}
					that.file_ss = ss;
				}, 1000)
			});
			//错误回调
			recorderManager_hf.onError((res) => {
				uni.hideLoading();
				uni.hideToast();
				console.log(res);
			})
		},
		//停止录音
		stop_hf() {
			var that = this;
			recorderManager_hf.stop();
			recorderManager_hf.onStop((res) => {
				console.log(res);
				that.tempFilePath = res.tempFilePath;
				that.file = res.tempFilePath;
				that.star_recorder = false;
				clearInterval(recordTimeInterval_a);
				recordTimeInterval_a = "";
			})
		},
		//播放声音
		play_hf() {
			var that = this;
			console.log('bofang');
			Mp.src = this.file;
			console.log(Mp);
			console.log(Mp.src);
			Mp.play();
			Mp.onPlay(() => {
				console.log('播放');
				that.recorder_play = true;
			})
			Mp.onEnded((re) => {
				that.recorder_play = false;
			})
			Mp.onError((re) => {
				console.log(re);
			})
		},
		recorder_pause() {
			var that = this;
			Mp.pause();
			Mp.onPause(() => {
				that.recorder_play = false;
			})
		},
		/**
		 * 隐藏模态对话框
		 */
		hideModal() {
			this.huoqu_tie = false;
			this.huoqu_ping = false;
			this.del_mod = false;
			this.del_msg = false;
			this.tz_del_msg = false;
			this.select = true;
			this.jubao = false;
			this.reasonText = '';
			this.visible1 = false;
			this.purchase_paper_mod = false;
			this.modalComment = false;
			this.CommentInfoMod = false;
		},
		/**
		 * 删除
		 */
		del_do() {
			var that = this;
			var b = app.globalData.api_root + 'User/del_article_huifu';
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.uid = e.uid;
			params.paper_id = this.info['id'];
			params.id = this.huifu_id;
			params.is_qq_text = this.reasonText;
			params.del_type = this.del_type;
			var huifu_list = that.huifu_list;
			http.POST(b, {
				params: params,
				success: (res) => {
					if (res.data.status == "success") {

						huifu_list.splice(that.huifu_key, 1);
						that.huifu_list = huifu_list;
						that.get_reply_and_list = [];
						that.get_reply_and_page = 1;
						that.hideModal();
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						that.get_reply_and_hui();
						//that.hui_fu_page(1);
					} else {
						that.del_mod = false;
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 删除帖子
		 */
		del_tz_do() {
			var that = this;
			var b = app.globalData.api_root + 'User/del_article';
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.uid = e.uid;
			params.paper_id = this.info['id'];
			// if (this.tz_del_msg == true) {
			//   if (this.is_qq_text == '') {
			//     uni.showToast({
			//       title: '内容不能为空',
			//       icon: 'none',
			//       duration: 2000
			//     })
			//     return;
			//   }
			// }
			params.is_qq_text = this.reasonText;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
					if (res.data.status == "success") {
						that.hideModal();
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
						setTimeout(() => {

							that._navback();
						}, 1500);
					} else {
						that.del_mod = false;
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						})
					}
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},
		/**
		 * 信息站点
		 */
		authority() {
			console.log(http.POST);
			var b = app.globalData.api_root + 'User/get_authority';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(2);
					console.log(res);
					this.copyright = res.data;
					app.globalData.store.setState({
						copyright: res.data
					});
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},

		/**
		 * 用户转发记录
		 */
		user_forward(id) {
			var b = app.globalData.api_root + 'Task/user_forwarded';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			params.id = id;
			http.POST(b, {
				params: params,
				success: (res) => {
					console.log(res);
				},
				fail: () => {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: (res) => { }
					})
				},
			})
		},




		/**
		 * 检测是否登陆状态过期
		 */
		check_login() {
			var n = this;
			uni.checkSession({
				success() {
					console.log(1);
					// session_key 未过期，并且在本生命周期一直有效
					n.check_phone_show = true;
				},
				fail() {
					console.log(2);
					n.check_phone_show = false;
					uni.showModal({
						title: '提示',
						content: '您的登录状态已过期，点击确定更新！',
						showCancel: false,
						success(res) {
							n.do_login();
						}
					})

					// session_key 已经失效，需要重新执行登录流程

				}
			})
		},
		_navback() {
			uni.navigateBack()
		},
		openUrl(url) {
			uni.navigateTo({
				url: url
			})
		},
		do_login() {
			var n = this;
			uni.login({
				success(o) {
					var b = app.globalData.api_root + 'Login/index';
					var params = new Object()
					params.code = o.code;
					http.POST(b, {
						params: params,
						success: (open) => {
							console.log(open);
							var data = new Object();
							data.openid = open.data.info.openid;
							data.session_key = open.data.info.session_key;
							http.POST(app.globalData.api_root + 'Login/add_tourist', {
								params: data,
								success: (d) => {
									console.log(d);
									app.globalData.setCache("userinfo", d.data.info);
									n.check_phone_show = true;
								}
							})
						}
					})
				}
			})
		},
		/**
		 * 删除原因
		 */
		handleReasonInput(e) {
			this.reasonText = e.detail.value;
		},
		/**
		 * 评论
		 */
		handleReplyButtonClick(e) {
			var checkLogin = app.globalData.checkPhoneLogin(1);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}

		}
	},

}
</script>
<style>
page {
	background-color: #f0f8ff;
}

/* 清爽青春风格基础样式 */
.img_bord {
	word-break: break-all;
	line-height: 1.8;
	min-height: 200rpx;
}

.no_ff {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.iphoneX-height {
	bottom: 35px !important;
}

.iphoneX-height-and {
	bottom: 402rpx !important;
}

.img_bord text {
	line-height: 1.8;
}

/* 清爽头部样式 */
.bg-gradient-header {
	background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
	color: #fff;
}

.header-back-text {
	color: #fff;
	font-weight: 500;
	font-size: 28rpx;
}

.header-title {
	color: #000000;
	font-weight: 600;
	font-size: 36rpx;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 清爽帖子标题卡片 */
.post-title-card {
	background-color: #fff;
	padding: 35rpx 30rpx;
	margin: 15rpx 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(78, 205, 196, 0.15);
	border: 1rpx solid rgba(78, 205, 196, 0.1);
	position: relative;
	overflow: hidden;
}

.post-title-content {
	font-size: 34rpx;
	font-weight: 600;
	line-height: 1.6;
	color: #2c3e50;
	position: relative;
	z-index: 2;
}

.post-title-decoration {
	position: absolute;
	right: 25rpx;
	top: 25rpx;
	font-size: 32rpx;
	opacity: 0.2;
	z-index: 1;
	color: #4ECDC4;
}

/* 清爽用户信息卡片 */
.user-info-card {
	background-color: #fff;
	padding: 25rpx 30rpx;
	margin: 15rpx 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(78, 205, 196, 0.12);
	border: 1rpx solid rgba(78, 205, 196, 0.08);
	display: flex;
	align-items: center;
}

.user-avatar-section {
	position: relative;
	margin-right: 25rpx;
}

.enhanced-avatar {
	width: 90rpx;
	height: 90rpx;
	border-radius: 50%;
	background-size: cover;
	background-position: center;
	position: relative;
	border: 2rpx solid rgba(78, 205, 196, 0.3);
}

.gender-badge {
	position: absolute;
	right: -5rpx;
	top: -5rpx;
	width: 28rpx;
	height: 28rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18rpx;
	z-index: 10;
	border: 2rpx solid #fff;
}

.female-badge {
	background-color: #FFB6C1;
	color: #fff;
}

.male-badge {
	background-color: #4ECDC4;
	color: #fff;
}

.avatar-frame {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
}

.user-details-section {
	flex: 1;
}

.user-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.user-nickname {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-right: 10rpx;
}

.user-badges {
	display: flex;
	align-items: center;
}

.vip-badge,
.level-badge,
.merit-badge {
	height: 32rpx;
	width: 32rpx;
	margin-left: 10rpx;
}

.post-meta-row {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #999;
}

.post-time {
	margin-right: 20rpx;
}

.post-status-badges {
	display: flex;
}

.status-badge {
	padding: 6rpx 16rpx;
	border-radius: 25rpx;
	font-size: 20rpx;
	margin-right: 12rpx;
	color: #fff;
	font-weight: 500;
}

.reply-level-badge {
	width: 40rpx;
	height: 40rpx;
}

.top-badge {
	background-color: #FF6B6B;
}

.essence-badge {
	background-color: #4ECDC4;
}

.more-actions-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: rgba(78, 205, 196, 0.1);
}

.more-icon {
	font-size: 36rpx;
	color: #4ECDC4;
}

/* 清爽帖子内容卡片 */
.post-content-card {
	background-color: #fff;
	padding: 35rpx 30rpx;
	margin: 15rpx 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(78, 205, 196, 0.12);
	border: 1rpx solid rgba(78, 205, 196, 0.08);
	font-size: 30rpx;
	line-height: 1.8;
}

.topic-tag {
	display: inline-flex;
	align-items: center;
	background-color: rgba(78, 205, 196, 0.1);
	border-radius: 25rpx;
	padding: 10rpx 20rpx;
	margin-bottom: 25rpx;
	border: 1rpx solid rgba(78, 205, 196, 0.2);
}

.topic-icon {
	margin-right: 8rpx;
	font-size: 24rpx;
}

.topic-name {
	font-size: 24rpx;
	color: #4ECDC4;
	font-weight: 500;
}

/* 重新设计的音频播放器 */
.audio-player-card {
	display: flex;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	border-radius: 20rpx;
	overflow: hidden;
	margin: 30rpx 20rpx;
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
	min-height: 200rpx;
	position: relative;
}

.audio-cover-section {
	position: relative;
	width: 200rpx;
	flex-shrink: 0;
}

.audio-cover {
	position: relative;
	width: 100%;
	height: 200rpx;
	background-size: cover;
	background-position: center;
	background-color: #333;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.cover-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
	z-index: 1;
}

.play-button-wrapper {
	position: relative;
	z-index: 10;
}

.audio-play-btn {
	width: 100rpx;
	height: 100rpx;
	background-color: rgba(255, 255, 255, 0.95);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
}

.audio-play-btn:active {
	transform: scale(0.95);
}

.audio-play-btn.playing {
	background-color: rgba(102, 126, 234, 0.95);
	color: #fff;
}

.audio-play-btn .cuIcon-play,
.audio-play-btn .cuIcon-pause {
	font-size: 50rpx;
	color: #333;
}

.audio-play-btn.playing .cuIcon-pause {
	color: #fff;
}

/* 播放状态波纹效果 */
.audio-wave-effect {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 5;
}

.wave-circle {
	position: absolute;
	border: 2rpx solid rgba(255, 255, 255, 0.6);
	border-radius: 50%;
	animation: wave-expand 2s ease-out infinite;
}

.wave-1 {
	width: 120rpx;
	height: 120rpx;
	margin: -60rpx 0 0 -60rpx;
	animation-delay: 0s;
}

.wave-2 {
	width: 160rpx;
	height: 160rpx;
	margin: -80rpx 0 0 -80rpx;
	animation-delay: 0.5s;
}

.wave-3 {
	width: 200rpx;
	height: 200rpx;
	margin: -100rpx 0 0 -100rpx;
	animation-delay: 1s;
}

@keyframes wave-expand {
	0% {
		transform: scale(0.8);
		opacity: 1;
	}

	100% {
		transform: scale(1.2);
		opacity: 0;
	}
}

.audio-info-section {
	flex: 1;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	color: #fff;
}

.audio-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 15rpx;
}

.audio-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #fff;
	line-height: 1.3;
	flex: 1;
	margin-right: 20rpx;
}

.audio-duration {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	background-color: rgba(255, 255, 255, 0.2);
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	font-weight: 500;
}

.audio-artist {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.9);
	margin-bottom: 25rpx;
	font-weight: 500;
}

.audio-progress-section {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.time-display {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 500;
	min-width: 80rpx;
}

.progress-container {
	flex: 1;
}

.audio-slider {
	margin: 0;
	height: 6rpx;
}

/* 位置信息卡片 */
.location-card {
	background-color: #fff;
	padding: 20rpx;
	margin: 20rpx;
	border-radius: 15rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.location-content {
	display: flex;
	align-items: center;
}

.location-icon {
	font-size: 36rpx;
	margin-right: 10rpx;
	color: #667eea;
}

.location-text {
	flex: 1;
	font-size: 28rpx;
	color: #666;
}

.location-arrow {
	font-size: 28rpx;
	color: #999;
}

/* 回复导航栏 */
.reply-nav-card {
	background-color: #fff;
	padding: 20rpx;
	margin: 20rpx;
	border-radius: 15rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.reply-nav-title {
	font-size: 30rpx;
	font-weight: 600;
	margin-bottom: 20rpx;
	color: #333;
}

.reply-nav-tabs {
	display: flex;
	border-bottom: 1rpx solid #f0f0f0;
}

.nav-tab {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 0;
	position: relative;
}

.nav-tab.active {
	color: #667eea;
}

.nav-tab.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 40rpx;
	height: 6rpx;
	background-color: #667eea;
	border-radius: 3rpx;
}

.nav-tab-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.nav-tab-text {
	font-size: 24rpx;
}



/* 原有样式保留 */
.classify {
	height: 100%;
	margin: 25rpx;
	text-align: center;
	background-color: #fff;
	border-radius: 25rpx;
	box-sizing: border-box;
	display: inline-block;
	position: relative;
}

.palette {
	width: 100% !important;
	height: 100% !important;
}

.cu-chat .cu-item>.main .content::after {
	top: -9rpx !important;
	left: 16rpx !important;
}

.yes_pos {
	position: relative;
}

.zan_style_test {
	float: right;
	margin-right: 20rpx;
	font-size: 12px;
}

.zan_style {
	float: right;
	margin-right: 4rpx;
}

.weui-tabbar {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	position: fixed;
	z-index: 500;
	bottom: 0;
	width: 100%;
	background-color: #f7f7fa;
}

.liwu_col {
	box-sizing: border-box;
	text-align: center;
	padding-top: 10rpx;
	display: inline-block;
	min-width: 25%;
	height: 210rpx;
}

.liwu_border {
	border: 1px solid #fbc2eb;
	box-shadow: 0px 0px 10px 0px #fbc2eb;
	border-radius: 10px;
}

.not_pos {
	width: 47% !important;
}

.wx_liwu {
	width: 88%;
	background: #fff;
	height: 78rpx;
	line-height: 72rpx;
	margin-top: 5%;
}

.input_number_red {
	float: right;
	border: 1px solid #a29bfe;
}

.input_number_blue {
	float: right;
	border: 1px solid #667eea;
}

button::after {
	line-height: normal;
	font-size: 30rpx;
	width: 0;
	height: 0;
	top: 0;
	left: 0;
}

button {
	line-height: normal;
	display: block;
	padding-left: 0px;
	padding-right: 0px;
	background-color: rgba(255, 255, 255, 0);
	font-size: 30rpx;
	overflow: inherit;
}

.bg-stripes-red {
	background-color: hsla(4, 31%, 58%, .07) !important;
	background-image: linear-gradient(225deg, rgba(229, 77, 66, .5) 10%, transparent 0, transparent 50%, rgba(229, 77, 66, .5) 0, rgba(229, 77, 66, .5) 60%, transparent 0, transparent);
	background-size: 7.07px 7.07px;
	z-index: -1;
}

.dasheds {
	border: 2rpx dashed rgba(119, 119, 119, 0.25);
	border-radius: 10rpx;
	background-color: #fff;
	margin-top: 10px;
}

.border-orange {
	border-color: #a29bfe !important;
}

.border-blue {
	border-color: #667eea !important;
}

/* 简洁的回复项样式 */
.reply-item-card {
	background-color: #fff;
	margin: 12rpx 20rpx;
	border-radius: 15rpx;
	box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.08);
	border: 1rpx solid rgba(78, 205, 196, 0.05);
	overflow: hidden;
	transition: all 0.3s ease;
	position: relative;
}

.reply-header {
	display: flex;
	align-items: flex-start;
	padding: 20rpx 25rpx 15rpx;
	border-bottom: 1rpx solid rgba(78, 205, 196, 0.1);
}

.reply-avatar {
	width: 75rpx;
	height: 75rpx;
	border-radius: 50%;
	background-size: cover;
	background-position: center;
	position: relative;
	border: 2rpx solid rgba(78, 205, 196, 0.2);
}

.reply-gender-badge {
	position: absolute;
	right: -5rpx;
	top: -5rpx;
	width: 26rpx;
	height: 26rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16rpx;
	z-index: 10;
	border: 2rpx solid #fff;
}

.reply-gender-badge.female {
	background-color: #FFB6C1;
	color: #fff;
}

.reply-gender-badge.male {
	background-color: #4ECDC4;
	color: #fff;
}

/* 回复头像框样式 */
.reply-avatar-frame {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	z-index: 5;
}

.reply-user-info {
	flex: 1;
	min-width: 0;
}

.reply-user-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
	flex-wrap: wrap;
}

.reply-username {
	font-size: 28rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-right: 12rpx;
}

/* 简化的回复用户徽章样式 */
.reply-user-badges {
	display: flex;
	align-items: center;
}

.reply-vip-badge,
.reply-level-badge,
.reply-merit-badge {
	height: 28rpx;
	width: 28rpx;
	margin-left: 8rpx;
}

.reply-meta-row {
	display: flex;
	align-items: center;
	font-size: 22rpx;
	color: #7f8c8d;
	flex-wrap: wrap;
	gap: 12rpx;
}

.author-badge {
	background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
	color: #fff;
	padding: 3rpx 10rpx;
	border-radius: 12rpx;
	font-size: 18rpx;
	font-weight: 500;
}

.reply-time {
	font-size: 22rpx;
}

.reply-actions {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.like-section {
	background-color: rgba(78, 205, 196, 0.1);
	padding: 6rpx 12rpx;
	border-radius: 15rpx;
	border: 1rpx solid rgba(78, 205, 196, 0.2);
}

.like-btn {
	font-size: 28rpx;
	margin-right: 6rpx;
	transition: transform 0.2s ease;
	vertical-align: middle;
	color: #4ECDC4;
}

.like-btn:active {
	transform: scale(1.1);
}

.like-btn.liked {
	animation: heartbeat 0.6s ease;
	color: #FF6B6B;
}

@keyframes heartbeat {
	0% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.3);
	}

	100% {
		transform: scale(1);
	}
}

.like-count {
	font-size: 22rpx;
	color: #7f8c8d;
	font-weight: 500;
	vertical-align: middle;
}

.more-btn {
	font-size: 36rpx;
	color: #999;
	border-radius: 50%;
}

/* 简化的回复内容区域 */
.reply-content-section {
	padding: 20rpx 25rpx 25rpx;
}

.reply-text-content {
	margin-bottom: 15rpx;
}

.reply-text {
	font-size: 26rpx;
	line-height: 1.7;
	color: #2c3e50;
	word-break: break-all;
}

.reply-gift-content {
	display: flex;
	align-items: flex-start;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	padding: 20rpx;
	border-radius: 15rpx;
	color: #fff;
}

.gift-icon {
	font-size: 32rpx;
	margin-right: 10rpx;
	animation: bounce 2s infinite;
}

@keyframes bounce {

	0%,
	20%,
	50%,
	80%,
	100% {
		transform: translateY(0);
	}

	40% {
		transform: translateY(-10rpx);
	}

	60% {
		transform: translateY(-5rpx);
	}
}

/* 回复图片 */
.reply-image-section {
	margin: 20rpx 0;
}

.reply-image {
	max-width: 200rpx;
	max-height: 200rpx;
	border-radius: 10rpx;
	object-fit: cover;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

/* 子回复区域 */
.sub-replies-section {
	background-color: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	margin-top: 20rpx;
	border-left: 4rpx solid #667eea;
}

.sub-replies-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

.sub-replies-icon {
	margin-right: 8rpx;
	font-size: 28rpx;
}

.sub-reply-item {
	margin-bottom: 12rpx;
	padding: 12rpx 15rpx;
	background-color: #fff;
	border-radius: 10rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.sub-reply-item:last-child {
	margin-bottom: 0;
}

.sub-reply-username {
	font-size: 26rpx;
	font-weight: 600;
	color: #667eea;
	margin-right: 8rpx;
}

.sub-reply-content {
	font-size: 26rpx;
	line-height: 1.5;
	color: #555;
	display: inline;
}

/* 红包标识 */
.reply-red-badge {
	position: absolute;
	top: -15rpx;
	right: 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	color: #fff;
	padding: 8rpx 15rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
	display: flex;
	align-items: center;
	animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
	from {
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
	}

	to {
		box-shadow: 0 6rpx 25rpx rgba(102, 126, 234, 0.6);
	}
}

.red-badge-icon {
	margin-right: 5rpx;
	font-size: 24rpx;
}

.red-badge-text {
	font-size: 20rpx;
}

.voice-player-card {
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	border-radius: 20rpx;
	padding: 25rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.voice-player-card:active {
	transform: scale(0.98);
}

.voice-icon-container {
	width: 80rpx;
	height: 80rpx;
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	position: relative;
}

.voice-play-icon {
	font-size: 40rpx;
	color: #fff;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.1);
	}

	100% {
		transform: scale(1);
	}
}

.voice-playing-gif {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
}

.voice-info {
	flex: 1;
	color: #fff;
}

.voice-label {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	margin-bottom: 8rpx;
}

.voice-duration {
	font-size: 24rpx;
	opacity: 0.9;
}

.voice-wave-animation {
	position: absolute;
	right: 20rpx;
	top: 50%;
	transform: translateY(-50%);
	display: flex;
	align-items: center;
	gap: 4rpx;
}

.wave-bar {
	width: 4rpx;
	height: 20rpx;
	background-color: rgba(255, 255, 255, 0.8);
	border-radius: 2rpx;
	animation: wave-animation 1.5s ease-in-out infinite;
}

.wave-bar:nth-child(1) {
	animation-delay: 0s;
}

.wave-bar:nth-child(2) {
	animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
	animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
	animation-delay: 0.6s;
}

@keyframes wave-animation {

	0%,
	100% {
		height: 20rpx;
	}

	50% {
		height: 40rpx;
	}
}

/* ==================
   优化后的评论模态框样式
 ==================== */
.comment-modal {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.4);
	z-index: 9999;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
}

.comment-modal.show {
	opacity: 1;
	visibility: visible;
}

.comment-modal-content {
	position: absolute;
	bottom: 0px;
	width: 100%;
	max-height: 80vh;
	background-color: #fff;
	border-top-left-radius: 25rpx;
	border-top-right-radius: 25rpx;
	transform: translateY(100%);
	transition: transform 0.3s ease;
	overflow: hidden;
}

.comment-modal.show .comment-modal-content {
	transform: translateY(0);
}

/* 简化的模态框头部 */
.comment-modal-header {
	padding: 25rpx 30rpx 20rpx;
	text-align: center;
	position: relative;
	border-bottom: 1rpx solid rgba(78, 205, 196, 0.1);
}

.modal-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #2c3e50;
	padding: 15rpx 0rpx;
}

/* 标签页导航 */
.comment-tabs-nav {
	display: flex;
	background-color: #f8f9fa;
	border-bottom: 1rpx solid #e9ecef;
}

.tab-item {
	flex: 1;
	padding: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
}

.tab-item.active {
	background-color: #fff;
	color: #667eea;
}

.tab-icon {
	font-size: 36rpx;
	margin-bottom: 8rpx;
}

.tab-text {
	font-size: 24rpx;
	font-weight: 500;
}

/* 标签页内容区域 */
.comment-tabs-content {
	min-height: 400rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.tab-content {
	padding: 30rpx;
}

/* 图文标签页样式 */
.text-tab {
	background-color: #fff;
}

.comment-input-section {
	margin-bottom: 20rpx;
}

.comment-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 25rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 15rpx;
	font-size: 28rpx;
	line-height: 1.5;
	color: #333;
	background-color: #f8f9fa;
	resize: none;
	transition: all 0.3s ease;
}

.comment-textarea:focus {
	border-color: #667eea;
	background-color: #fff;
	box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.input-tools-bar {
	display: flex;
	align-items: center;
	margin-top: 20rpx;
	gap: 20rpx;
}

.tool-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f9fa;
	color: #666;
	font-size: 40rpx;
	transition: all 0.3s ease;
	cursor: pointer;
}

.tool-btn:active {
	transform: scale(0.95);
}

.tool-btn.active {
	background-color: #667eea;
	color: #fff;
}

/* 表情选择器样式 */
.emoji-picker {
	width: 100%;
	height: 400rpx;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	margin-top: 20rpx;
	overflow: hidden;
}

.emoji-swiper {
	height: 100%;
}

.emoji-grid {
	display: grid;
	grid-template-columns: repeat(9, 1fr);
	gap: 15rpx;
	padding: 20rpx;
	justify-items: center;
}

.emoji-item {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 10rpx;
	transition: all 0.2s ease;
	cursor: pointer;
}

.emoji-item:active {
	background-color: #e9ecef;
	transform: scale(1.1);
}

.emoji-image {
	width: 50rpx;
	height: 50rpx;
}

/* 图片预览区域样式 */
.image-preview-section {
	margin-top: 20rpx;
}

.image-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 15rpx;
}

.image-item {
	position: relative;
	aspect-ratio: 1;
	border-radius: 10rpx;
	overflow: hidden;
}

.preview-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.image-remove-btn {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 40rpx;
	height: 40rpx;
	background-color: #ff4757;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 24rpx;
	z-index: 10;
	cursor: pointer;
}

/* 自选昵称样式 */
.nickname-section {
	margin-top: 20rpx;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	text-align: center;
}

.nickname-option {
	display: inline-block;
}

.nickname-btn {
	padding: 15rpx 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	color: #fff;
	border-radius: 25rpx;
	font-size: 26rpx;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.nickname-selected {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
}

.selected-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
}

.selected-name {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.cancel-btn {
	padding: 8rpx 20rpx;
	background-color: #ff4757;
	color: #fff;
	border-radius: 20rpx;
	font-size: 22rpx;
	cursor: pointer;
}

/* 语音标签页样式 */
.voice-tab {
	background-color: #fff;
	text-align: center;
}

.voice-recorder-section {
	padding: 40rpx 20rpx;
}

.audio-player-container {
	margin-bottom: 60rpx;
}

.modal-audio-player-card {
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
	margin: 0 auto;
	max-width: 500rpx;
}

.audio-controls {
	display: flex;
	align-items: center;
}

.audio-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 40rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.audio-btn:active {
	transform: scale(0.95);
	background-color: rgba(255, 255, 255, 0.3);
}

.audio-visualizer {
	display: flex;
	align-items: center;
	gap: 6rpx;
	margin: 0 20rpx;
}

.wave-bar {
	width: 6rpx;
	height: 30rpx;
	background-color: rgba(255, 255, 255, 0.8);
	border-radius: 3rpx;
	animation: wave-pulse 1.5s ease-in-out infinite;
}

.wave-bar:nth-child(1) {
	animation-delay: 0s;
}

.wave-bar:nth-child(2) {
	animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
	animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
	animation-delay: 0.6s;
}

.wave-bar:nth-child(5) {
	animation-delay: 0.8s;
}

.wave-bar:nth-child(6) {
	animation-delay: 1s;
}

.wave-bar:nth-child(7) {
	animation-delay: 1.2s;
}

.wave-bar:nth-child(8) {
	animation-delay: 1.4s;
}

@keyframes wave-pulse {

	0%,
	100% {
		height: 30rpx;
	}

	50% {
		height: 60rpx;
	}
}

.audio-duration {
	color: #fff;
	font-size: 28rpx;
	font-weight: 600;
}

.record-button-container {
	margin: 60rpx 0;
}

.record-button {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	box-shadow: 0 10rpx 30rpx rgba(255, 107, 107, 0.4);
	transition: all 0.3s ease;
	cursor: pointer;
}

.record-button:active {
	transform: scale(0.95);
}

.record-button.recording {
	animation: recording-pulse 1.5s ease-in-out infinite;
}

@keyframes recording-pulse {

	0%,
	100% {
		transform: scale(1);
		box-shadow: 0 10rpx 30rpx rgba(255, 107, 107, 0.4);
	}

	50% {
		transform: scale(1.05);
		box-shadow: 0 15rpx 40rpx rgba(255, 107, 107, 0.6);
	}
}

.record-icon {
	width: 120rpx;
	height: 120rpx;
}

.record-tip {
	font-size: 26rpx;
	color: #666;
	margin-top: 30rpx;
}

/* 礼物标签页样式 */
.gift-tab {
	background-color: #fff;
}

.gift-section {
	padding: 20rpx;
}

.gift-scroll {
	white-space: nowrap;
	margin-bottom: 30rpx;
}

.gift-list {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 0;
}

.gift-item {
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	text-align: center;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	cursor: pointer;
}

.gift-item.selected {
	border-color: #4a89dc;
	background-color: #e3f2fd;
}

.gift-icon {
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 10rpx;
}

.gift-name {
	font-size: 24rpx;
	color: #333;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.gift-price {
	font-size: 22rpx;
	color: #666;
}

.balance-info {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	margin-bottom: 30rpx;
}

.balance-label {
	font-size: 26rpx;
	color: #666;
}

.currency-icon {
	width: 40rpx;
	height: 40rpx;
}

.balance-amount {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
}

.reward-button {
	width: 100%;
	padding: 25rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	border: none;
	border-radius: 25rpx;
	font-size: 30rpx;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.reward-button:active {
	transform: scale(0.98);
}

/* 底部操作按钮样式 */
.comment-modal-footer {
	display: flex;
	justify-content: space-between;
	padding: 25rpx 30rpx;
	background-color: #f8f9fa;
	border-top: 1rpx solid #e9ecef;
	gap: 20rpx;
}

.footer-btn {
	flex: 1;
	padding: 25rpx;
	border: none;
	border-radius: 25rpx;
	font-size: 28rpx;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
}

.cancel-btn {
	background-color: #6c757d;
	color: #fff;
}

.submit-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	color: #fff;
}

.footer-btn:active {
	transform: scale(0.98);
}

/*按钮大小  */
.audioOpen {
	width: 50rpx;
	height: 50rpx;
	border: 1px solid #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	margin-top: 57rpx;
}

/* 简化的打赏排行榜样式 */
.reward-ranking-card {
	background: #fff;
	margin: 15rpx 20rpx;
	border-radius: 15rpx;
	padding: 25rpx;
	box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.1);
	border: 1rpx solid rgba(78, 205, 196, 0.1);
	position: relative;
	overflow: hidden;
}

.ranking-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
	position: relative;
	z-index: 2;
}

.ranking-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.ranking-icon {
	font-size: 28rpx;
	color: #4ECDC4;
}

.ranking-text {
	font-size: 26rpx;
	font-weight: 600;
	color: #2c3e50;
}

.ranking-subtitle {
	font-size: 22rpx;
	color: #7f8c8d;
	font-weight: 400;
}

.ranking-avatars {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 15rpx;
	position: relative;
	z-index: 2;
}

.ranking-avatar-item {
	display: flex;
	align-items: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.ranking-avatar-item:active {
	transform: scale(0.95);
}

.avatar-container {
	position: relative;
}

.ranking-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	border: 2rpx solid rgba(78, 205, 196, 0.3);
	transition: all 0.3s ease;
}

.ranking-avatar.placeholder {
	opacity: 0.6;
	filter: grayscale(100%);
}

.rank-1 .ranking-avatar {
	width: 70rpx;
	height: 70rpx;
	border-color: #FFD700;
}

.rank-2 .ranking-avatar {
	width: 65rpx;
	height: 65rpx;
	border-color: #C0C0C0;
}

.rank-3 .ranking-avatar {
	width: 60rpx;
	height: 60rpx;
	border-color: #CD7F32;
}

.ranking-badge {
	position: absolute;
	bottom: -5rpx;
	left: 0px;
	width: 60rpx;
}

.reward-action {
	text-align: right;
	position: relative;
	z-index: 2;
}

.reward-btn {
	background: rgba(78, 205, 196, 0.1);
	border: 1rpx solid rgba(78, 205, 196, 0.3);
	border-radius: 25rpx;
	padding: 15rpx 25rpx;
	display: inline-flex;
	align-items: center;
	gap: 8rpx;
	transition: all 0.3s ease;
	cursor: pointer;
}

.reward-btn:active {
	transform: scale(0.98);
	background: rgba(78, 205, 196, 0.15);
}

.reward-btn-icon {
	font-size: 24rpx;
	color: #4ECDC4;
}

/* 优化后的点评按钮样式 */
.review-button-container {
	background-color: #ffffff;
	padding: 30rpx;
	margin: 20rpx 0;
}

.review-button-card {
	background: linear-gradient(135deg, #667eea 0%, #7c8ce8 100%);
	border: 1rpx solid rgba(102, 126, 234, 0.3);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.review-button-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.review-button-card:active {
	transform: translateY(2rpx) scale(0.98);
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.35) 0%, rgba(116, 185, 255, 0.4) 100%);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);
}

.review-button-content {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 1;
}

.review-icon-wrapper {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
	backdrop-filter: blur(10rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.review-icon {
	font-size: 36rpx;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.review-text-section {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.review-main-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 8rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	letter-spacing: 1rpx;
}

.review-sub-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.review-arrow {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.review-arrow text {
	font-size: 28rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.reward-btn-text {
	font-size: 24rpx;
	font-weight: 500;
	color: #4ECDC4;
}

/* 现代化点评模态框样式 */
.modern-comment-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 9999;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.modern-comment-modal.show {
	opacity: 1;
	visibility: visible;
}

.modern-comment-dialog {
	width: 100%;
	max-width: 600rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
	transform: scale(0.8) translateY(50rpx);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modern-comment-modal.show .modern-comment-dialog {
	transform: scale(1) translateY(0);
}

/* 头部样式 */
.modern-comment-header {
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
}

.modern-comment-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.header-content {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 1;
}

.header-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

.header-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.header-title-l {
	font-size: 36rpx;
	font-weight: 600;
	color: #000000;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.header-close {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	z-index: 1;
	backdrop-filter: blur(10rpx);
}

.header-close:active {
	transform: scale(0.9);
	background: rgba(255, 255, 255, 0.3);
}

.header-close text {
	font-size: 32rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 评分卡片样式 */
.rating-card {
	padding: 30rpx;
	text-align: center;
	background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
	margin: 30rpx;
	border-radius: 20rpx;
	box-shadow: 0 8rpx 25rpx rgba(250, 177, 160, 0.3);
}

.rating-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #2d3436;
	margin-bottom: 20rpx;
	text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.rating-stars {
	display: flex;
	justify-content: center;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.rating-star {
	font-size: 50rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	filter: grayscale(100%);
	opacity: 0.5;
}

.rating-star.active {
	filter: grayscale(0%);
	opacity: 1;
	transform: scale(1.1);
	text-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.6);
}

.rating-star:active {
	transform: scale(0.9);
}

.rating-description {
	font-size: 24rpx;
	color: rgba(45, 52, 54, 0.7);
	font-style: italic;
}

/* 评语输入卡片样式 */
.comment-input-card {
	margin: 30rpx;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
	border: 2rpx solid rgba(102, 126, 234, 0.1);
}

.input-label {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.input-icon {
	font-size: 28rpx;
	margin-right: 15rpx;
}

.input-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #2d3436;
}

.input-wrapper {
	position: relative;
}

.modern-textarea {
	width: 100%;
	height: 200rpx;
	padding: 25rpx;
	border: 2rpx solid rgba(102, 126, 234, 0.2);
	border-radius: 15rpx;
	font-size: 28rpx;
	line-height: 1.6;
	color: #2d3436;
	background: rgba(248, 249, 250, 0.5);
	transition: all 0.3s ease;
	resize: none;
}

.modern-textarea:focus {
	border-color: #667eea;
	background: #ffffff;
	box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

/* 选项卡片样式 */
.options-card {
	margin: 30rpx;
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
	border: 2rpx solid rgba(102, 126, 234, 0.1);
}

.option-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	cursor: pointer;
	transition: all 0.3s ease;
}

.option-item:last-child {
	border-bottom: none;
}

.option-item:active {
	background: rgba(102, 126, 234, 0.05);
}

.option-content {
	display: flex;
	align-items: center;
}

.option-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
}

.option-text {
	font-size: 30rpx;
	color: #2d3436;
	font-weight: 500;
}

/* 现代化开关样式 */
.modern-switch {
	width: 80rpx;
	height: 44rpx;
	background: #ddd;
	border-radius: 22rpx;
	position: relative;
	transition: all 0.3s ease;
	cursor: pointer;
}

.modern-switch.active {
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
}

.switch-handle {
	width: 36rpx;
	height: 36rpx;
	background: #ffffff;
	border-radius: 50%;
	position: absolute;
	top: 4rpx;
	left: 4rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.modern-switch.active .switch-handle {
	transform: translateX(36rpx);
}

.option-arrow {
	width: 50rpx;
	height: 50rpx;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.option-arrow text {
	font-size: 24rpx;
	color: #667eea;
}

/* 提交按钮样式 */
.submit-section {
	padding: 10rpx 30rpx 30rpx 30rpx;
}

.modern-submit-btn {
	background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
	border-radius: 20rpx;
	padding: 25rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 8rpx 25rpx rgba(0, 184, 148, 0.3);
	position: relative;
	overflow: hidden;
}

.modern-submit-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.modern-submit-btn:active {
	transform: translateY(2rpx) scale(0.98);
	box-shadow: 0 4rpx 15rpx rgba(0, 184, 148, 0.4);
}

.submit-icon {
	font-size: 32rpx;
	filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.3));
}

.submit-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 紧凑清爽的点评列表样式 */
.modern-reviews-container {
	background: transparent;
	padding: 0rpx 20rpx 15rpx 20rpx;
}

/* 简化的点评列表头部 */
.reviews-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 15rpx;
	padding: 20rpx 25rpx;
	background: #fff;
	border-radius: 15rpx;
	box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.1);
	border: 1rpx solid rgba(78, 205, 196, 0.1);
}

.reviews-title {
	display: flex;
	align-items: center;
}

.reviews-icon {
	font-size: 28rpx;
	margin-right: 12rpx;
	color: #4ECDC4;
}

.reviews-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #2c3e50;
}

.reviews-count {
	font-size: 22rpx;
	color: #7f8c8d;
	background: rgba(78, 205, 196, 0.1);
	padding: 6rpx 15rpx;
	border-radius: 20rpx;
}

/* 紧凑的点评列表容器 */
.reviews-list {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

/* 简化的点评卡片样式 */
.review-card {
	background: #ffffff;
	border-radius: 15rpx;
	padding: 20rpx 25rpx;
	box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.08);
	border: 1rpx solid rgba(78, 205, 196, 0.1);
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.review-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 4rpx;
	height: 100%;
	background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
	opacity: 0;
	transition: all 0.3s ease;
}

.review-card:active {
	transform: translateY(1rpx) scale(0.99);
	box-shadow: 0 3rpx 12rpx rgba(78, 205, 196, 0.15);
}

.review-card:active::before {
	opacity: 1;
}

/* 简化的用户信息区域 */
.review-user-section {
	display: flex;
	align-items: center;
	margin-bottom: 18rpx;
}

.review-avatar-wrapper {
	position: relative;
	margin-right: 20rpx;
}

.review-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	border: 2rpx solid rgba(78, 205, 196, 0.3);
	transition: all 0.3s ease;
}

.review-privacy-badge {
	position: absolute;
	bottom: -3rpx;
	right: -3rpx;
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid #ffffff;
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
	z-index: 10;
}

.review-privacy-badge.private {
	background: #FFB6C1;
}

.review-privacy-badge.public {
	background: #4ECDC4;
}

.privacy-icon {
	font-size: 16rpx;
}

/* 简化的用户信息 */
.review-user-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.review-username {
	font-size: 26rpx;
	font-weight: 600;
	color: #2c3e50;
	line-height: 1.2;
}

.review-rating {
	display: flex;
	gap: 6rpx;
}

.review-star {
	font-size: 22rpx;
	opacity: 0.3;
	transition: all 0.3s ease;
	filter: grayscale(100%);
}

.review-star.active {
	opacity: 1;
	filter: grayscale(0%);
	color: #FFD700;
}

.review-arrow {
	width: 40rpx;
	height: 40rpx;
	background: rgba(78, 205, 196, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

/* 简化的点评内容区域 */
.review-content-section {
	position: relative;
}

.review-privacy-tag {
	display: inline-block;
	padding: 4rpx 12rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
	font-weight: 500;
	margin-bottom: 10rpx;
}

.review-privacy-tag.private {
	background: rgba(255, 182, 193, 0.2);
	color: #FFB6C1;
}

.review-privacy-tag.public {
	background: rgba(78, 205, 196, 0.2);
	color: #4ECDC4;
}

.review-content-text {
	font-size: 26rpx;
	line-height: 1.6;
	color: #7f8c8d;
	word-break: break-all;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

/* 简化的查看更多按钮区域 */
.more-reviews-section {
	margin-top: 20rpx;
}

.more-reviews-btn {
	background: #fff;
	border: 1rpx solid rgba(78, 205, 196, 0.3);
	border-radius: 15rpx;
	padding: 20rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.1);
	position: relative;
	overflow: hidden;
}

.more-reviews-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(78, 205, 196, 0.05);
	opacity: 0;
	transition: all 0.3s ease;
	pointer-events: none;
}

.more-reviews-btn:active {
	transform: translateY(1rpx) scale(0.99);
	box-shadow: 0 3rpx 12rpx rgba(78, 205, 196, 0.15);
}

.more-reviews-btn:active::before {
	opacity: 1;
}

.more-btn-content {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	position: relative;
	z-index: 1;
}

.more-btn-icon {
	font-size: 28rpx;
	color: #4ECDC4;
}

.more-btn-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #4ECDC4;
}

.more-btn-arrow {
	font-size: 24rpx;
	color: #4ECDC4;
	transition: all 0.3s ease;
}

.more-reviews-btn:active .more-btn-arrow {
	transform: translateX(3rpx);
}

/* 现代化点评详情模态框样式 */
.modern-comment-detail-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 9999;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.modern-comment-detail-modal.show {
	opacity: 1;
	visibility: visible;
}

.modern-comment-detail-dialog {
	width: 100%;
	max-width: 600rpx;
	background: #ffffff;
	border-radius: 24rpx;
	overflow: hidden;
	transform: scale(0.8) translateY(50rpx);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modern-comment-detail-modal.show .modern-comment-detail-dialog {
	transform: scale(1) translateY(0);
}

/* 头部样式 */
.comment-detail-header {
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	padding: 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
}

.comment-detail-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.comment-detail-header .header-content {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 1;
}

.comment-detail-header .header-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
}

.comment-detail-header .header-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.comment-detail-header .header-close {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	z-index: 1;
	backdrop-filter: blur(10rpx);
}

.comment-detail-header .header-close:active {
	transform: scale(0.9);
	background: rgba(255, 255, 255, 0.3);
}

.comment-detail-header .header-close text {
	font-size: 32rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 用户信息区域 */
.comment-user-section {
	padding: 30rpx;
	display: flex;
	align-items: center;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);
}

.user-avatar-wrapper {
	margin-right: 25rpx;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 3rpx solid rgba(102, 126, 234, 0.3);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.user-info {
	flex: 1;
}

.user-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #2d3436;
	margin-bottom: 8rpx;
}

.user-time {
	font-size: 24rpx;
	color: #636e72;
}

/* 评分区域 */
.comment-rating-section {
	padding: 20rpx 30rpx;
	text-align: center;
	background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
	position: relative;
}

.rating-stars-wrapper {
	display: flex;
	justify-content: center;
	gap: 15rpx;
}

.detail-rating-star {
	font-size: 50rpx;
	filter: grayscale(100%);
	opacity: 0.5;
	transition: all 0.3s ease;
}

.detail-rating-star.active {
	filter: grayscale(0%);
	opacity: 1;
	text-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.6);
}

.rating-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #2d3436;
	margin-bottom: 10rpx;
	text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.rating-score {
	font-size: 24rpx;
	color: rgba(45, 52, 54, 0.7);
	font-weight: 500;
}

/* 评语内容区域 */
.comment-content-section {
	padding: 30rpx;
	background: #ffffff;
}

.content-label {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.content-icon {
	font-size: 28rpx;
	margin-right: 15rpx;
}

.content-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #2d3436;
}

.content-text {
	font-size: 30rpx;
	line-height: 1.8;
	color: #636e72;
	background: rgba(248, 249, 250, 0.8);
	padding: 25rpx;
	border-radius: 15rpx;
	border: 2rpx solid rgba(102, 126, 234, 0.1);
	word-break: break-all;
	min-height: 120rpx;
}

/* 操作按钮区域 */
.comment-actions-section {
	padding: 30rpx;
	background: #f8f9fa;
}

.action-buttons {
	display: flex;
	gap: 20rpx;
}

.action-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.action-btn:active {
	transform: translateY(2rpx) scale(0.98);
}

.delete-btn {
	background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
	box-shadow: 0 6rpx 20rpx rgba(255, 118, 117, 0.3);
}

.delete-btn:active {
	box-shadow: 0 3rpx 10rpx rgba(255, 118, 117, 0.4);
}

.confirm-btn {
	background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
	box-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.3);
}

.confirm-btn:active {
	box-shadow: 0 3rpx 10rpx rgba(0, 184, 148, 0.4);
}

.btn-icon {
	font-size: 32rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
	position: relative;
	z-index: 1;
}

.btn-text {
	font-size: 30rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	position: relative;
	z-index: 1;
}

/* 现代化论坛声明样式 */
.forum-declaration-container {
	padding: 0rpx 20rpx;
}

.forum-declaration-card {
	background: #ffffff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
	border: 2rpx solid rgba(255, 193, 7, 0.2);
	position: relative;
}

.forum-declaration-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 6rpx;
	height: 100%;
	background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
}

/* 声明标题区域 */
.declaration-header {
	background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
	padding: 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 2rpx solid rgba(255, 193, 7, 0.2);
	position: relative;
}

.header-content {
	display: flex;
	align-items: center;
}

.header-icon {
	font-size: 36rpx;
	margin-right: 20rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(255, 152, 0, 0.3));
}

.header-title-n {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.header-badge {
	background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
	padding: 8rpx 20rpx;
	border-radius: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
}

.badge-text {
	font-size: 22rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 声明内容区域 */
.declaration-content {
	padding: 30rpx;
	background: #ffffff;
	position: relative;
}

.declaration-content::before {
	content: '';
	position: absolute;
	top: 0;
	left: 30rpx;
	right: 30rpx;
	height: 2rpx;
	background: linear-gradient(90deg, transparent 0%, rgba(255, 193, 7, 0.3) 50%, transparent 100%);
}

.declaration-text {
	font-size: 24rpx !important;
	line-height: 1.8 !important;
	color: #495057 !important;
	letter-spacing: 1rpx !important;
	word-break: break-all;
	text-align: justify;
}

/* 针对rich-text内部元素的样式 */
.declaration-text>>>* {
	font-size: 24rpx !important;
	line-height: 1.8 !important;
	color: #495057 !important;
	margin: 0 !important;
	padding: 0 !important;
}

.declaration-text>>>p {
	margin-bottom: 15rpx !important;
}

.declaration-text>>>strong,
.declaration-text>>>b {
	color: #856404 !important;
	font-weight: 600 !important;
}

.declaration-text>>>em,
.declaration-text>>>i {
	color: #6c757d !important;
	font-style: italic !important;
}

/* 现代化弹窗样式优化 */
.cu-modal .cu-dialog {
	border-radius: 24rpx !important;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(78, 205, 196, 0.2) !important;
	border: 2rpx solid rgba(78, 205, 196, 0.1);
}

.cu-modal .cu-bar {
	color: white !important;
	position: relative;
}

.cu-modal .cu-bar::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.cu-modal .cu-bar .content {
	font-weight: 600 !important;
	font-size: 34rpx !important;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	position: relative;
	z-index: 1;
}

.cu-modal .cu-bar .action {
	position: relative;
	z-index: 1;
}

.cu-modal .cu-bar .action text {
	color: white;
	font-size: 40rpx !important;
	opacity: 0.9;
	transition: all 0.3s ease;
}

.cu-modal .cu-bar .action:active text {
	opacity: 1;
	transform: scale(1.1);
}

/* 弹窗内容区域优化 */
.cu-modal .padding,
.cu-modal .padding-sm {
	background: #ffffff;
	position: relative;
}

.cu-modal .padding::before {
	content: '';
	position: absolute;
	top: 0;
	left: 30rpx;
	right: 30rpx;
	height: 2rpx;
	background: linear-gradient(90deg, transparent 0%, rgba(78, 205, 196, 0.3) 50%, transparent 100%);
}

/* 操作按钮优化 */
.cu-modal .cf .bg-yellow {
	background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%) !important;
	color: white !important;
	border-radius: 20rpx !important;
	box-shadow: 0 8rpx 20rpx rgba(78, 205, 196, 0.3) !important;
	transition: all 0.3s ease !important;
	border: none !important;
	font-weight: 500 !important;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.cu-modal .cf .bg-yellow:active {
	transform: translateY(2rpx) scale(0.98) !important;
	box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.4) !important;
}

/* 文本输入区域优化 */
.cu-modal .weui-textarea {
	background: #ffffff !important;
	border: 2rpx solid rgba(78, 205, 196, 0.2) !important;
	border-radius: 15rpx !important;
	padding: 25rpx !important;
	font-size: 28rpx !important;
	line-height: 1.6 !important;
	color: #333333 !important;
	transition: all 0.3s ease !important;
	box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.1) !important;
	width: 100%;
}

.cu-modal .weui-textarea:focus {
	border-color: #4ECDC4 !important;
	box-shadow: 0 0 0 6rpx rgba(78, 205, 196, 0.1) !important;
	background: #ffffff !important;
}

/* 禁言选项优化 */
.cu-modal .bg-red {
	background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%) !important;
	color: white !important;
	border-radius: 20rpx !important;
	box-shadow: 0 8rpx 20rpx rgba(78, 205, 196, 0.3) !important;
	transition: all 0.3s ease !important;
	position: relative;
}

.cu-modal .bg-red::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.cu-modal .bg-red:active {
	transform: translateY(2rpx) scale(0.98) !important;
	box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.4) !important;
}

.cu-modal .bg-red .text-lg {
	font-weight: 600 !important;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
	position: relative;
	z-index: 1;
}

.cu-modal .bg-red .text-Abc {
	opacity: 0.9;
	position: relative;
	z-index: 1;
}

/* 底部操作栏优化 */
.cu-modal .cu-bar.justify-center {
	background: #ffffff !important;
	border-top: 2rpx solid rgba(78, 205, 196, 0.1) !important;
	padding: 30rpx !important;
}

.cu-modal .cu-bar.justify-center view {
	padding: 20rpx 40rpx !important;
	border-radius: 25rpx !important;
	font-weight: 500 !important;
	font-size: 30rpx !important;
	transition: all 0.3s ease !important;
	margin: 0 15rpx !important;
	min-width: 120rpx !important;
	text-align: center !important;
}

.cu-modal .cu-bar.justify-center view:first-child {
	background: rgba(78, 205, 196, 0.1) !important;
	color: #4ECDC4 !important;
	border: 2rpx solid rgba(78, 205, 196, 0.3) !important;
}

.cu-modal .cu-bar.justify-center view:last-child {
	background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%) !important;
	color: white !important;
	box-shadow: 0 8rpx 20rpx rgba(78, 205, 196, 0.3) !important;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.cu-modal .cu-bar.justify-center view:active {
	transform: translateY(2rpx) scale(0.98) !important;
}

/* 现代化圈子信息样式 */
.circle-info-container {
	padding: 20rpx;
}

.circle-info-card {
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
	border: 2rpx solid rgba(102, 126, 234, 0.1);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.circle-info-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 6rpx;
	height: 100%;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	opacity: 0;
	transition: all 0.3s ease;
}

.circle-info-card:active {
	transform: translateY(2rpx) scale(0.98);
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
}

.circle-info-card:active::before {
	opacity: 1;
}

/* 圈子头像区域 */
.circle-avatar-section {
	margin-right: 25rpx;
}

.avatar-wrapper {
	position: relative;
	width: 100rpx;
	height: 100rpx;
}

.circle-avatar {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	transition: all 0.3s ease;
}

.avatar-border {
	position: absolute;
	top: -4rpx;
	left: -4rpx;
	right: -4rpx;
	bottom: -4rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	z-index: -1;
	opacity: 0;
	transition: all 0.3s ease;
}

.circle-info-card:active .avatar-border {
	opacity: 1;
}

.circle-info-card:active .circle-avatar {
	transform: scale(0.95);
}

/* 圈子信息区域 */
.circle-details-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.circle-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #2d3436;
	line-height: 1.2;
	margin-bottom: 5rpx;
}

.circle-stats {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.stat-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.stat-icon {
	font-size: 24rpx;
}

.stat-number {
	font-size: 26rpx;
	font-weight: 600;
	color: #667eea;
}

.stat-label {
	font-size: 24rpx;
	color: #636e72;
}

.stat-divider {
	color: #ddd;
	font-size: 24rpx;
}

/* 进入按钮区域 */
.circle-action-section {
	margin-left: 20rpx;
}

.enter-btn {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
	position: relative;
	overflow: hidden;
}

.enter-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.enter-btn:active {
	transform: scale(0.9);
	box-shadow: 0 3rpx 10rpx rgba(102, 126, 234, 0.4);
}

.enter-icon {
	font-size: 36rpx;
	color: #ffffff;
	font-weight: bold;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	position: relative;
	z-index: 1;
	transition: all 0.3s ease;
}

.circle-info-card:active .enter-icon {
	transform: translateX(3rpx);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.circle-info-container {
		padding: 20rpx;
		margin: 15rpx 0;
	}

	.circle-info-card {
		padding: 25rpx;
	}

	.avatar-wrapper {
		width: 80rpx;
		height: 80rpx;
	}

	.circle-name {
		font-size: 30rpx;
	}

	.stat-number {
		font-size: 24rpx;
	}

	.enter-btn {
		width: 70rpx;
		height: 70rpx;
	}

	.enter-icon {
		font-size: 32rpx;
	}
}

/* 回复列表空状态样式 */
.reply-empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 60rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	margin: 30rpx;
	border-radius: 25rpx;
	position: relative;
	overflow: hidden;
	min-height: 400rpx;
}

.reply-empty-state::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
	pointer-events: none;
}

/* 空状态动画容器 */
.empty-animation-container {
	position: relative;
	margin-bottom: 40rpx;
	width: 120rpx;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.empty-icon-main {
	font-size: 80rpx;
	animation: bounce 2s infinite;
	filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.empty-icon-floating {
	position: absolute;
	font-size: 30rpx;
	top: -10rpx;
	right: -10rpx;
	animation: float 3s ease-in-out infinite;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.empty-icon-floating-2 {
	position: absolute;
	font-size: 25rpx;
	bottom: -5rpx;
	left: -15rpx;
	animation: float 3s ease-in-out infinite 1.5s;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

/* 空状态内容 */
.empty-content {
	text-align: center;
	margin-bottom: 50rpx;
	position: relative;
	z-index: 1;
}

.empty-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #2d3436;
	margin-bottom: 20rpx;
	text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.empty-subtitle {
	font-size: 28rpx;
	color: #636e72;
	line-height: 1.6;
	max-width: 400rpx;
}

/* 空状态操作按钮 */
.empty-action {
	position: relative;
	z-index: 1;
}

.empty-comment-btn {
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	padding: 25rpx 50rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	gap: 15rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
	position: relative;
	overflow: hidden;
}

.empty-comment-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.empty-comment-btn:active {
	transform: translateY(2rpx) scale(0.98);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
}

.empty-btn-icon {
	font-size: 32rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
	position: relative;
	z-index: 1;
}

.empty-btn-text {
	font-size: 30rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	position: relative;
	z-index: 1;
}

/* 动画定义 */
@keyframes bounce {

	0%,
	20%,
	50%,
	80%,
	100% {
		transform: translateY(0);
	}

	40% {
		transform: translateY(-10rpx);
	}

	60% {
		transform: translateY(-5rpx);
	}
}

@keyframes float {

	0%,
	100% {
		transform: translateY(0) rotate(0deg);
		opacity: 0.7;
	}

	50% {
		transform: translateY(-15rpx) rotate(10deg);
		opacity: 1;
	}
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.reply-empty-state {
		padding: 80rpx 40rpx;
		margin: 20rpx;
		min-height: 350rpx;
	}

	.empty-icon-main {
		font-size: 70rpx;
	}

	.empty-title {
		font-size: 32rpx;
	}

	.empty-subtitle {
		font-size: 26rpx;
	}

	.empty-comment-btn {
		padding: 20rpx 40rpx;
	}

	.empty-btn-text {
		font-size: 28rpx;
	}
}

/* 轻盈的底部操作栏样式 */
.modern-bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.95);
	padding: 15rpx 25rpx 25rpx;
	display: flex;
	align-items: center;
	gap: 15rpx;
	box-shadow: 0 -2rpx 12rpx rgba(78, 205, 196, 0.1);
	border-top: 1rpx solid rgba(78, 205, 196, 0.1);
	z-index: 10001;
	backdrop-filter: blur(20rpx);
}

.modern-bottom-bar.iphonex-padding {
	padding-bottom: 60rpx;
}

/* 轻盈的评论输入区域 */
.comment-input-section {
	flex: 1;
	margin-right: 15rpx;
}

.modern-comment-input {
	background: #fff;
	border-radius: 30rpx;
	padding: 20rpx 25rpx;
	display: flex;
	align-items: center;
	gap: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1rpx solid rgba(78, 205, 196, 0.2);
	position: relative;
	overflow: hidden;
}

.modern-comment-input::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(78, 205, 196, 0.05);
	opacity: 0;
	transition: all 0.3s ease;
	pointer-events: none;
}

.modern-comment-input:active {
	transform: scale(0.99);
	border-color: rgba(78, 205, 196, 0.4);
}

.modern-comment-input:active::before {
	opacity: 1;
}

.comment-input-icon {
	font-size: 28rpx;
	color: #4ECDC4;
	position: relative;
	z-index: 1;
}

.comment-input-placeholder {
	font-size: 26rpx;
	color: #95a5a6;
	position: relative;
	z-index: 1;
}

/* 轻盈的操作按钮区域 */
.action-buttons-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.action-btn-wrapper {
	position: relative;
}

.action-btn {
	width: 70rpx;
	height: 70rpx;
	background: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1rpx solid rgba(78, 205, 196, 0.2);
	position: relative;
}

.action-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(78, 205, 196, 0.1);
	opacity: 0;
	transition: all 0.3s ease;
	pointer-events: none;
}

.action-btn:active {
	transform: scale(0.95);
}

.action-btn:active::before {
	opacity: 1;
}

.action-btn.active {
	background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
	border-color: rgba(78, 205, 196, 0.4);
	box-shadow: 0 2rpx 8rpx rgba(78, 205, 196, 0.3);
}

.action-btn.active::before {
	background: rgba(255, 255, 255, 0.1);
	opacity: 1;
}

.action-icon {
	font-size: 32rpx;
	transition: all 0.3s ease;
	position: relative;
	z-index: 1;
	color: #4ECDC4;
}

.action-btn.active .action-icon {
	color: #fff;
}

/* 简化的徽章样式 */
.action-badge {
	position: absolute;
	top: -6rpx;
	right: -6rpx;
	min-width: 28rpx;
	height: 28rpx;
	border-radius: 14rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 18rpx;
	font-weight: 600;
	color: #ffffff;
	padding: 0 6rpx;
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
	border: 2rpx solid #ffffff;
}

.action-badge.yellow {
	background: #FF6B6B;
}

.action-badge.blue {
	background: #4ECDC4;
}

.action-badge.red {
	background: #FFB6C1;
}

/* 现代化分享弹窗样式 */
.modern-share-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 10000;
	display: flex;
	align-items: flex-end;
	justify-content: center;
	animation: fadeIn 0.3s ease;
}

.share-modal-content {
	width: 100%;
	background: #ffffff;
	border-top-left-radius: 25rpx;
	border-top-right-radius: 25rpx;
	padding-bottom: env(safe-area-inset-bottom);
	animation: slideUp 0.3s ease;
	box-shadow: 0 -8rpx 30rpx rgba(0, 0, 0, 0.2);
}

/* 弹窗头部 */
.share-modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 30rpx 30rpx;
	border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);
}

.share-title {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.share-icon {
	font-size: 36rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.share-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #2d3436;
}

.share-close {
	width: 60rpx;
	height: 60rpx;
	background: rgba(0, 0, 0, 0.05);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.share-close:active {
	transform: scale(0.9);
	background: rgba(0, 0, 0, 0.1);
}

.share-close text {
	font-size: 28rpx;
	color: #636e72;
}

/* 分享选项 */
.share-options {
	padding: 40rpx 30rpx 50rpx;
	display: flex;
	gap: 30rpx;
	justify-content: center;
}

.share-option-item {
	flex: 1;
	max-width: 200rpx;
}

.share-btn {
	width: 100%;
	background: transparent;
	border: none;
	padding: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.share-btn:active {
	transform: scale(0.95);
}

.share-btn-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.share-btn-icon::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.wechat-btn .share-btn-icon {
	background: linear-gradient(135deg, #07c160 0%, #00ae56 100%);
	box-shadow: 0 8rpx 25rpx rgba(7, 193, 96, 0.3);
}

.moments-btn .share-btn-icon {
	background: linear-gradient(135deg, #1296db 0%, #0d7377 100%);
	box-shadow: 0 8rpx 25rpx rgba(18, 150, 219, 0.3);
}

.share-btn:active .share-btn-icon {
	transform: scale(0.9);
}

.share-emoji {
	font-size: 50rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
	position: relative;
	z-index: 1;
}

.share-btn-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #2d3436;
	text-align: center;
}

/* 动画定义 */
@keyframes fadeIn {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}

	to {
		transform: translateY(0);
	}
}

/* 现代化投票组件样式 */
.modern-vote-container {
	margin: 20rpx;
}

.modern-vote-card {
	background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
	border-radius: 24rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.12);
	border: 1rpx solid rgba(102, 126, 234, 0.1);
	position: relative;
	overflow: hidden;
}

.modern-vote-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 6rpx;
	background: linear-gradient(90deg, #667eea 0%, #a29bfe 100%);
}

/* 投票标题区域 */
.vote-header-section {
	margin-bottom: 40rpx;
}

.vote-type-badge {
	display: inline-flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 24rpx;
	border-radius: 50rpx;
	margin-bottom: 24rpx;
	font-size: 24rpx;
	font-weight: 600;
}

.vote-type-badge.single-choice {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.vote-type-badge.multiple-choice {
	background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
	color: #ffffff;
	box-shadow: 0 4rpx 16rpx rgba(162, 155, 254, 0.3);
}

.vote-type-icon {
	font-size: 28rpx;
}

.vote-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #2d3436;
	line-height: 1.5;
	margin-bottom: 8rpx;
}

/* 投票选项区域 */
.vote-options-section {
	margin-bottom: 40rpx;
}

.vote-option-item {
	position: relative;
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
	transition: all 0.3s ease;
	cursor: pointer;
}

.vote-option-item:last-child {
	margin-bottom: 0;
}

.vote-option-item:not(.option-disabled):hover {
	transform: translateY(-2rpx);
}

.vote-option-item:not(.option-disabled):active {
	transform: translateY(0);
}

.option-content {
	display: flex;
	align-items: center;
	padding: 25rpx 20rpx;
	background: #ffffff;
	border-radius: 16rpx;
	transition: all 0.3s ease;
	position: relative;
	z-index: 2;
}

.vote-option-item.option-selected .option-content {
	border-color: #667eea;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(162, 155, 254, 0.05) 100%);
	box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.15);
}

.vote-option-item.option-disabled .option-content {
	background: #f8f9fa;
	cursor: default;
}

.option-selector {
	margin-right: 20rpx;
}

.selector-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	border: 3rpx solid #dee2e6;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	background: #ffffff;
}

.selector-icon.multiple {
	border-radius: 8rpx;
}

.selector-icon.selected {
	border-color: #667eea;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

/* 未选中状态的选择器 */
.selector-icon:not(.selected) {
	border-color: #dee2e6;
	background: #ffffff;
}

.selector-icon:not(.selected):hover {
	border-color: #667eea;
	background: rgba(102, 126, 234, 0.05);
}

.check-icon {
	color: #ffffff;
	font-size: 24rpx;
	font-weight: 600;
}

.option-text {
	flex: 1;
	font-size: 28rpx;
	color: #2d3436;
	font-weight: 500;
	line-height: 1.4;
}

.option-votes {
	font-size: 24rpx;
	color: #667eea;
	font-weight: 600;
	padding: 8rpx 16rpx;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 20rpx;
}

/* 进度条样式 */
.option-progress-bar {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 8rpx;
	background: rgba(102, 126, 234, 0.15);
	z-index: 100;
	border-radius: 0 0 16rpx 16rpx;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #667eea 0%, #a29bfe 100%);
	border-radius: 0 0 16rpx 16rpx;
	transition: width 0.8s ease;
	position: relative;
	min-width: 2rpx;
}

.progress-fill::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
	animation: shimmer 2s infinite;
}

@keyframes shimmer {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(100%);
	}
}

/* 投票信息区域 */
.vote-info-section {
	border-top: 1rpx solid rgba(102, 126, 234, 0.1);
	padding-top: 30rpx;
}

.vote-meta-info {
	display: flex;
	flex-wrap: wrap;
	gap: 24rpx;
	margin-bottom: 30rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 26rpx;
	color: #636e72;
}

.meta-item.expired {
	color: #e17055;
}

.meta-icon {
	font-size: 28rpx;
}

.meta-text {
	font-weight: 500;
}

/* 投票按钮 */
.vote-action-section {
	display: flex;
	justify-content: center;
}

.modern-vote-btn {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #a29bfe 100%);
	color: #ffffff;
	border: none;
	border-radius: 50rpx;
	font-size: 28rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
	cursor: pointer;
	min-width: 200rpx;
	justify-content: center;
}

.modern-vote-btn:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
}

.modern-vote-btn:active {
	transform: translateY(0);
}

.modern-vote-btn.btn-disabled {
	background: #bdc3c7;
	box-shadow: none;
	cursor: not-allowed;
}

.vote-btn-icon {
	font-size: 32rpx;
}

.vote-btn-text {
	font-size: 28rpx;
}

/* 已投票状态 */
.vote-completed-section {
	display: flex;
	justify-content: center;
}

.completed-badge {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 32rpx;
	background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
	color: #ffffff;
	border-radius: 50rpx;
	font-size: 26rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 16rpx rgba(0, 184, 148, 0.3);
}

.completed-icon {
	font-size: 28rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.share-modal-header {
		padding: 35rpx 25rpx 25rpx;
	}

	.share-options {
		padding: 35rpx 25rpx 45rpx;
		gap: 25rpx;
	}

	.share-btn-icon {
		width: 100rpx;
		height: 100rpx;
	}

	.share-emoji {
		font-size: 45rpx;
	}

	.share-btn-text {
		font-size: 26rpx;
	}

	.modern-vote-card {
		padding: 30rpx;
		margin: 15rpx;
	}

	.vote-title {
		font-size: 30rpx;
	}

	.option-content {
		padding: 20rpx 24rpx;
	}

	.option-text {
		font-size: 26rpx;
	}

	.modern-vote-btn {
		padding: 18rpx 36rpx;
		font-size: 26rpx;
		min-width: 180rpx;
	}
}

/* ==================
   美观简洁的活动详情卡片样式
 ==================== */

/* 活动容器 */
.elegant-activity-container {
	margin: 24rpx;
}

.elegant-activity-card {
	background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 6rpx 24rpx rgba(102, 126, 234, 0.12);
	border: 1rpx solid rgba(102, 126, 234, 0.08);
}

/* 头部认证区域 */
.activity-header-section {
	padding: 24rpx 30rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
}

.certification-badge {
	display: inline-flex;
	align-items: center;
	gap: 12rpx;
	padding: 12rpx 20rpx;
	border-radius: 25rpx;
	font-size: 26rpx;
	font-weight: 600;
}

.certification-badge.certified {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
	color: #ffffff;
	box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.25);
}

.certification-badge.uncertified {
	background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
	color: #6b46c1;
	box-shadow: 0 4rpx 12rpx rgba(196, 181, 253, 0.25);
}

.cert-icon {
	font-size: 28rpx;
}

/* 活动信息网格 */
.activity-info-grid {
	padding: 30rpx;
}

.info-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.info-item:last-child {
	border-bottom: none;
}

.info-item.location-item {
	cursor: pointer;
}

.info-item.location-item:active {
	background: rgba(102, 126, 234, 0.05);
	border-radius: 12rpx;
	margin: 0 -16rpx;
	padding: 20rpx 16rpx;
}

.info-icon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	flex-shrink: 0;
	background: rgba(102, 126, 234, 0.1);
}

.location-bg {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.time-bg {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.people-bg {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.join-bg {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.price-bg {
	background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.icon-text {
	font-size: 28rpx;
	color: #667eea;
}

.info-details {
	flex: 1;
}

.info-label {
	font-size: 24rpx;
	color: #8e8e93;
	margin-bottom: 6rpx;
	display: block;
}

.info-value {
	font-size: 28rpx;
	color: #2c3e50;
	font-weight: 600;
	line-height: 1.4;
}

.free-text {
	color: #00b894;
}

.info-arrow {
	color: #667eea;
	font-size: 24rpx;
	margin-left: 12rpx;
}

/* 人数信息行 */
.info-row {
	display: flex;
	gap: 24rpx;
	padding: 20rpx 0;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.03);
}

.info-item-small {
	display: flex;
	align-items: center;
	flex: 1;
}

.info-icon-small {
	width: 64rpx;
	height: 64rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	background: rgba(102, 126, 234, 0.1);
}

.icon-text-small {
	font-size: 24rpx;
	color: #667eea;
}

.info-details-small {
	flex: 1;
}

.info-label-small {
	font-size: 22rpx;
	color: #8e8e93;
	margin-bottom: 4rpx;
	display: block;
}

.info-value-small {
	font-size: 26rpx;
	color: #2c3e50;
	font-weight: 600;
}

/* 验证码区域 */
.verification-section {
	padding: 24rpx 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.verification-card {
	text-align: center;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 16rpx;
	backdrop-filter: blur(10rpx);
}

.verification-label {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-bottom: 12rpx;
	display: block;
}

.verification-code {
	font-size: 48rpx;
	font-weight: 700;
	color: #ffffff;
	letter-spacing: 12rpx;
	font-family: monospace;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 参与者预览区域 */
.participants-section {
	padding: 24rpx 30rpx;
	background: rgba(102, 126, 234, 0.02);
}

.participants-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.participants-avatars {
	display: flex;
	align-items: center;
	gap: -12rpx;
}

.participant-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-size: cover;
	background-position: center;
	border: 3rpx solid #ffffff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 1;
}

.participant-avatar:hover {
	z-index: 2;
	transform: scale(1.05);
}

.more-participants {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	border: 3rpx solid #ffffff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.more-text {
	font-size: 20rpx;
	color: #ffffff;
	font-weight: 600;
}

.view-all-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: rgba(102, 126, 234, 0.1);
	color: #667eea;
	border-radius: 20rpx;
	font-size: 24rpx;
	cursor: pointer;
	border: 1rpx solid rgba(102, 126, 234, 0.2);
	transition: all 0.3s ease;
}

.view-all-btn:active {
	background: rgba(102, 126, 234, 0.15);
	transform: translateY(1rpx);
}

.view-all-arrow {
	font-size: 20rpx;
}

/* 操作按钮区域 */
.action-section {
	padding: 30rpx;
	background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.elegant-btn {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 28rpx;
	border: none;
	border-radius: 16rpx;
	font-size: 30rpx;
	font-weight: 700;
	transition: all 0.3s ease;
}

.join-btn {
	background: linear-gradient(135deg, #667eea 0%, #7c8ce8 100%);
	color: #ffffff;
	box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.2);
}

.join-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.ended-btn {
	background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
	color: #ffffff;
	box-shadow: 0 6rpx 20rpx rgba(149, 165, 166, 0.2);
}

.joined-btn {
	background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
	color: #ffffff;
	box-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.3);
}

.btn-icon {
	font-size: 32rpx;
}

/* 参与者详细列表 */
.participants-detail-list {
	max-height: 400rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.04);
}

.participant-detail-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.02);
}

.participant-detail-item:last-child {
	border-bottom: none;
}

.participant-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.participant-detail-avatar {
	width: 52rpx;
	height: 52rpx;
	border-radius: 50%;
	background-size: cover;
	background-position: center;
	border: 2rpx solid #ffffff;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.participant-detail-name {
	font-size: 26rpx;
	color: #2c3e50;
	font-weight: 500;
}

.participant-detail-time {
	font-size: 22rpx;
	color: #8e8e93;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.elegant-activity-container {
		margin: 20rpx;
	}

	.elegant-activity-card {
		border-radius: 16rpx;
	}

	.activity-header-section {
		padding: 20rpx 24rpx;
	}

	.certification-badge {
		padding: 10rpx 16rpx;
		font-size: 24rpx;
	}

	.activity-info-grid {
		padding: 24rpx;
	}

	.info-icon {
		width: 56rpx;
		height: 56rpx;
		margin-right: 16rpx;
	}

	.icon-text {
		font-size: 24rpx;
	}

	.info-value {
		font-size: 26rpx;
	}

	.verification-section {
		padding: 20rpx 24rpx;
	}

	.verification-code {
		font-size: 40rpx;
		letter-spacing: 8rpx;
	}

	.participants-section {
		padding: 20rpx 24rpx;
	}

	.participant-avatar {
		width: 52rpx;
		height: 52rpx;
	}

	.more-participants {
		width: 52rpx;
		height: 52rpx;
	}

	.action-section {
		padding: 24rpx;
	}

	.elegant-btn {
		padding: 24rpx;
		font-size: 28rpx;
	}

	.participant-detail-item {
		padding: 16rpx 24rpx;
	}

	.participant-detail-avatar {
		width: 44rpx;
		height: 44rpx;
	}

	.participant-detail-name {
		font-size: 24rpx;
	}

	.participant-detail-time {
		font-size: 20rpx;
	}
}
</style>