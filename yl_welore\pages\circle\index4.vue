<template>
    <view class="app-container">
        <!-- 顶部欢迎区域 -->
        <view class="welcome-header">
            <view class="welcome-content">
                <text class="welcome-title">🌟欢迎来到{{ design.landgrave }}世界</text>
                <text class="welcome-subtitle">发现精彩，连接同好</text>
            </view>
            <!-- <navigator v-if="copyright.tory_arbor == 1" url="" hover-class="none">
                <view class="quick-create-btn">
                    <text class="btn-emoji">✨</text>
                    <text class="btn-text">创建</text>
                </view>
            </navigator> -->
            <view v-if="copyright.tory_arbor == 1" class="quick-create-btn" @click="openUrl('/yl_welore/pages/packageB/set_territory/index')">
                <text class="btn-emoji">✨</text>
                <text class="btn-text">创建</text>
            </view>
        </view>

        <!-- 排行榜区域 -->
        <view v-if="rank.length > 0" class="rank-section">
            <view class="section-title-bar">
                <view class="header-content">
                    <text class="title-text">排行榜</text>
                    <text class="title-desc">看看大家都在关注什么</text>
                </view>
            </view>
            <scroll-view :scroll-x="true" class="rank-carousel">
                <view v-for="(item, index) in rank" :key="index" @tap="open_ph" :data-id="item.id"
                      class="rank-card" :class="'rank-' + (index + 1)">
                    <view class="rank-number">{{ index + 1 }}</view>
                    <view class="rank-content">
                        <view class="rank-icon">
                            <text v-if="index === 0" class="rank-emoji">🥇</text>
                            <text v-else-if="index === 1" class="rank-emoji">🥈</text>
                            <text v-else-if="index === 2" class="rank-emoji">🥉</text>
                            <text v-else class="rank-emoji">🏆</text>
                        </view>
                        <view class="rank-info">
                            <text class="rank-title">{{ item.ranking_name }}</text>
                            <text class="rank-subtitle">热度榜单</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- 我的圈子区域 -->
        <view class="my-circles-section">
            <view class="section-title-bar">
                <view class="header-content">
                    <text class="title-text">我的{{ design.landgrave }}</text>
                    <text class="title-desc">{{ info.length }}个已加入</text>
                </view>
            </view>

            <scroll-view v-if="info.length > 0" :scroll-x="true" class="circles-scroll">
                <view class="circles-container">
                    <view v-for="(item, index) in info" :key="index" @tap="this_url" :data-id="item.id" class="circle-card">
                        <view class="card-header">
                            <image class="circle-avatar" :src="item.realm_icon" mode="aspectFill"></image>
                            <view v-if="item.attention == 1" class="status-badge private">
                                <text class="badge-emoji">🔒</text>
                            </view>
                            <view v-if="item.attention == 2" class="status-badge vip">
                                <text class="badge-emoji">👑</text>
                            </view>
                        </view>
                        <view class="card-content">
                            <text class="circle-title">{{ item.realm_name }}</text>
                            <text class="circle-meta">点击进入</text>
                        </view>
                    </view>
                </view>
            </scroll-view>

            <view v-else class="empty-circles">
                <text class="empty-emoji">🌱</text>
                <text class="empty-title">还没有加入任何{{ design.landgrave }}</text>
                <text class="empty-desc">快去发现感兴趣的内容吧</text>
            </view>
        </view>

        <!-- 分类广场区域 -->
        <view class="plaza-section">
            <view class="section-title-bar">
                <view class="header-content">
                    <text class="title-text">{{ design.landgrave }}广场</text>
                </view>
                <navigator url="/yl_welore/pages/square/index" hover-class="none">
                    <text class="view-all-btn">查看全部</text>
                </navigator>
            </view>

            <scroll-view :scroll-x="true" class="plaza-carousel">
                <view v-for="(item, index) in needle" :key="index" class="plaza-category">
                    <view class="category-header" :style="'background-image: url(' + item.icon + ');'">
                        <view class="category-overlay">
                            <text class="category-name">{{ item.name }}</text>
                        </view>
                    </view>

                    <view class="category-items">
                        <view v-for="(c_item, c_index) in item.children.slice(0, 3)" :key="c_index" @tap="this_url"
                            :data-id="c_item.id" class="category-mini-card">
                            <image class="mini-avatar" :src="c_item.realm_icon" mode="aspectFill"></image>
                            <view class="mini-content">
                                <text class="mini-name">{{ c_item.realm_name }}</text>
                                <view class="mini-stats">
                                      <text class="cicon-accounts stats-emoji"></text>
                                    <text class="stats-text">{{ c_item.concern }}</text>
                                </view>
                            </view>
                            <view v-if="c_item.attention == 1" class="mini-badge private">🔒</view>
                            <view v-if="c_item.attention == 2" class="mini-badge vip">
                                <text class="cicon-crown"></text>
                            </view>
                        </view>

                        <view class="view-more-card" @tap="quan_url" :data-id="item.id">
                            <text class="more-text">查看更多</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- 推荐区域 -->
        <view class="recommend-section">
            <view class="section-title-bar">
                <view class="header-content">
                    <text class="title-text">为你推荐</text>
                    <text class="title-desc">基于你的兴趣</text>
                </view>
            </view>

            <view class="recommend-grid">
                <view v-for="(item, t_index) in tj_list" :key="t_index" @tap="this_url" :data-id="item.id"
                    class="recommend-card">
                    <view class="recommend-header">
                        <image class="recommend-avatar" :src="item.realm_icon" mode="aspectFill"></image>
                        <view class="recommend-badge">
                            <text class="badge-emoji">⭐</text>
                        </view>
                    </view>

                    <view class="recommend-body">
                        <text class="recommend-title">{{ item.realm_name }}</text>
                        <text class="recommend-description">{{ item.realm_synopsis }}</text>
                    </view>

                    <view class="recommend-footer">
                        <view class="join-btn">
                            <text class="join-text">立即加入</text>
                        </view>
                    </view>
                </view>
            </view>

            <view class="load-status">
                <view v-if="!di_msg" class="loading-state">
                    <text class="loading-emoji">⏳</text>
                    <text class="loading-text">正在为你寻找更多精彩内容...</text>
                </view>
                <view v-else class="complete-state">
                    <text class="complete-emoji">🎉</text>
                    <text class="complete-text">已为你展示所有推荐内容</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        rank() {
            return this.$parent.$data.rank;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        design() {
            return this.$parent.$data.design;
        },
        copyright() {
            return this.$parent.$data.copyright;
        },
        info() {
            return this.$parent.$data.info;
        },
        needle() {
            return this.$parent.$data.needle;
        },
        tj_list() {
            return this.$parent.$data.tj_list;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        },
    },
    methods: {
        open_ph(e) {
            this.$emit('open_ph', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url', e);
        },
        quan_url(e) {
            this.$emit('quan_url', e);
        },
        openUrl(url){
            uni.navigateTo({
                url: url
            });
        },
    }
};
</script>
<style scoped>
/* 全局样式重置 */
* {
    box-sizing: border-box;
}

/* 主容器样式 */
.app-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
    padding-bottom: 120rpx;
    position: relative;
    padding-top: 0;
}

/* 顶部欢迎区域 */
.welcome-header {
    padding: 110rpx 30rpx 40rpx 30rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    backdrop-filter: blur(20px);
    border-radius: 0 0 40rpx 40rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.05);
    min-height: 220rpx;
    position: relative;
}

.welcome-content {
    flex: 1;
}

.welcome-emoji {
    font-size: 48rpx;
    margin-bottom: 10rpx;
    display: block;
}

.welcome-title {
    font-size: 36rpx;
    font-weight: 700;
    color: #2c3e50;
    text-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.5);
    margin-bottom: 8rpx;
    display: block;
}

.welcome-subtitle {
    font-size: 26rpx;
    color: #5a6c7d;
    display: block;
}

.quick-create-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: #ffffff;
    border-radius: 50rpx;
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 10rpx 25rpx rgba(238, 90, 36, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    width: 180rpx;
    margin-top: 20rpx;
}

.quick-create-btn:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: 0 6rpx 15rpx rgba(238, 90, 36, 0.5);
}

.btn-emoji {
    font-size: 28rpx;
    margin-right: 8rpx;
}

.btn-text {
    font-size: 28rpx;
    font-weight: 600;
}

/* 通用区域标题栏 */
.section-title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx 20rpx;
    margin-bottom: 20rpx;
}

.section-title-bar .header-content {
    display: flex;
    align-items: center;
}

.title-emoji {
    font-size: 36rpx;
    margin-right: 8rpx;
}

.title-text {
    font-size: 36rpx;
    font-weight: 700;
    color: #2c3e50;
    text-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.5);
}

.title-desc {
    font-size: 24rpx;
    color: #5a6c7d;
    margin-left: 10rpx;
}

.view-all-btn {
    font-size: 26rpx;
    color: #5a6c7d;
    padding: 10rpx 20rpx;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 30rpx;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.view-all-btn:active {
    background: rgba(255, 255, 255, 0.8);
    transform: scale(0.95);
}

/* 排行榜区域样式 */
.rank-section {
    margin-bottom: 40rpx;
}

.rank-carousel {
    padding: 0 30rpx;
    white-space: nowrap;
}

.rank-card {
    position: relative;
    display: inline-block;
    width: 280rpx;
    height: 160rpx;
    margin-right: 20rpx;
    border-radius: 20rpx;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8rpx 5rpx rgba(0, 0, 0, 0.1);
}

.rank-card:active {
    transform: translateY(-5rpx) scale(1.02);
    box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);
}

/* 不同排名的渐变色 */
.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
}

.rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
}

.rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
}

.rank-4 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rank-5 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.rank-6 {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.rank-7 {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.rank-8 {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.rank-9 {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.rank-10 {
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

/* 超过10名的循环使用颜色 */
.rank-card:nth-child(11n+1) {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
}

.rank-card:nth-child(11n+2) {
    background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
}

.rank-card:nth-child(11n+3) {
    background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
}

.rank-card:nth-child(11n+4) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.rank-card:nth-child(11n+5) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.rank-card:nth-child(11n+6) {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.rank-card:nth-child(11n+7) {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.rank-card:nth-child(11n+8) {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.rank-card:nth-child(11n+9) {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.rank-card:nth-child(11n+10) {
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

.rank-number {
    position: absolute;
    top: 15rpx;
    right: 15rpx;
    width: 40rpx;
    height: 40rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22rpx;
    font-weight: 700;
    color: #333;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.rank-content {
    display: flex;
    align-items: center;
    padding: 25rpx;
    height: 100%;
}

.rank-icon {
    margin-right: 20rpx;
    flex-shrink: 0;
}

.rank-emoji {
    font-size: 48rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

.rank-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.rank-title {
    font-size: 26rpx;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.rank-subtitle {
    font-size: 20rpx;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 我的圈子区域样式 */
.my-circles-section {
    margin-bottom: 40rpx;
}

.circles-scroll {
    white-space: nowrap;
    padding: 0 30rpx;
}

.circles-container {
    display: inline-flex;
    flex-direction: column;
    flex-wrap: wrap;
    max-height: 420rpx;
    gap: 20rpx;
    padding-bottom: 20rpx;
}

.circle-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 25rpx;
    backdrop-filter: blur(10px);
    box-shadow: 0 10rpx 5rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 190rpx;
    height: 220rpx;
    display: inline-block;
    vertical-align: top;
}

.circle-card:active {
    transform: translateY(-5rpx) scale(1.02);
    box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);
}

.card-header {
    position: relative;
    margin-bottom: 15rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.circle-avatar {
    width: 85rpx;
    height: 85rpx;
    border-radius: 20rpx;
    border: 3rpx solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-badge {
    position: absolute;
    top: -15rpx;
    right: -15rpx;
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18rpx;
}

.status-badge.private {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.status-badge.vip {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
}

.card-content {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.circle-title {
    font-size: 26rpx;
    font-weight: 600;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.3;
}

.circle-meta {
    font-size: 22rpx;
    color: #666;
    font-weight: 500;
}

.empty-circles {
    text-align: center;
    padding: 80rpx 30rpx;
    color: #5a6c7d;
}

.empty-emoji {
    font-size: 80rpx;
    margin-bottom: 20rpx;
    display: block;
}

.empty-title {
    font-size: 28rpx;
    font-weight: 600;
    margin-bottom: 10rpx;
    display: block;
    color: #2c3e50;
}

.empty-desc {
    font-size: 24rpx;
    opacity: 0.8;
    display: block;
}

/* 分类广场区域样式 */
.plaza-section {
    margin-bottom: 40rpx;
}

.plaza-carousel {
    padding: 0 30rpx;
    white-space: nowrap;
}

.plaza-category {
    display: inline-block;
    width: 350rpx;
    margin-right: 25rpx;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24rpx;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.plaza-category:active {
    transform: translateY(-5rpx);
    box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);
}

.category-header {
    height: 160rpx;
    background-size: cover;
    background-position: center;
    position: relative;
}

.category-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 30rpx 20rpx 15rpx;
    display: flex;
    align-items: center;
}

.category-emoji {
    font-size: 28rpx;
    margin-right: 10rpx;
}

.category-name {
    font-size: 26rpx;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.category-items {
    padding: 20rpx;
}

.category-mini-card {
    display: flex;
    align-items: center;
    padding: 10rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    margin-bottom: 12rpx;
    position: relative;
    transition: all 0.3s ease;
}

.category-mini-card:active {
    background: #e9ecef;
    transform: scale(0.98);
}

.mini-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 12rpx;
    margin-right: 15rpx;
    border: 2rpx solid #ffffff;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.mini-content {
    flex: 1;
}

.mini-name {
    font-size: 26rpx;
    font-weight: 600;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.mini-stats {
    font-size: 20rpx;
    color: #666;
    padding-top: 10rpx;
}

.stats-emoji {
    font-size: 24rpx;
    margin-right: 5rpx;
    vertical-align: middle;
}

.stats-text {
    font-size: 20rpx;
    vertical-align: middle;
}

.mini-badge {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    font-size: 25rpx;
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.view-more-card {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: #ffffff;
    text-align: center;
    padding: 20rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.view-more-card:active {
    transform: scale(0.98);
    opacity: 0.9;
}

.more-emoji {
    font-size: 24rpx;
    margin-right: 8rpx;
}

.more-text {
    font-size: 24rpx;
    font-weight: 600;
}

/* 推荐区域样式 */
.recommend-section {
    margin-bottom: 40rpx;
}

.recommend-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    padding: 0 30rpx;
}

.recommend-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24rpx;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.recommend-card:active {
    transform: translateY(-8rpx) scale(1.02);
    box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.15);
}

.recommend-header {
    position: relative;
    padding: 20rpx;
    text-align: center;
}

.recommend-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 20rpx;
    border: 3rpx solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.recommend-badge {
    position: absolute;
    top: 15rpx;
    right: 15rpx;
    width: 32rpx;
    height: 32rpx;
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
}

.badge-emoji {
    font-size: 18rpx;
}

.recommend-body {
    padding: 0 20rpx 20rpx;
    text-align: center;
    min-height: 125rpx;
}

.recommend-title {
    font-size: 26rpx;
    font-weight: 700;
    color: #333;
    margin-bottom: 10rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.recommend-description {
    font-size: 22rpx;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-top:  15rpx;
}

.recommend-footer {
    padding: 0 20rpx 20rpx;
}

.join-btn {
    background: linear-gradient(135deg, #10ac84 0%, #00d2d3 100%);
    color: #ffffff;
    border-radius: 50rpx;
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: 600;
    box-shadow: 0 6rpx 15rpx rgba(16, 172, 132, 0.3);
    transition: all 0.3s ease;
}

.join-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 3rpx 8rpx rgba(16, 172, 132, 0.4);
}

.join-emoji {
    font-size: 20rpx;
    margin-right: 8rpx;
}

.join-text {
    font-size: 24rpx;
}

/* 加载状态样式 */
.load-status {
    text-align: center;
    padding: 40rpx 30rpx;
}

.loading-state,
.complete-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #5a6c7d;
}

.loading-emoji,
.complete-emoji {
    font-size: 48rpx;
    margin-bottom: 15rpx;
    animation: bounce 2s infinite;
}

.loading-text,
.complete-text {
    font-size: 26rpx;
    font-weight: 500;
}

/* 动画效果 */
@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10rpx);
    }

    60% {
        transform: translateY(-5rpx);
    }
}
</style>
